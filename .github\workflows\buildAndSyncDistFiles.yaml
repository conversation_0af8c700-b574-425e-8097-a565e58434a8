name: Build and Sync dist files

on:
  push:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repo A
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20'

      - name: Install pnpm
        run: npm install -g pnpm@9.0.0

      - name: Install Dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Bump version and Build
        id: bump
        run: |
          git config --global user.name '<PERSON> chao'
          git config --global user.email '<EMAIL>'
          npx vue-tsc --noEmit
          echo "newVersion=$(npm version patch)" >> $GITHUB_ENV
          git push --follow-tags https://${{secrets.GITHUB_TOKEN}}@github.com/liuc-c/jx3-wbl.git
          npx vite build --minify esbuild 

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.TM_TOKEN }}
        with:
          tag_name: ${{ env.newVersion }}
          release_name: Release ${{ env.newVersion }}
          draft: false
          prerelease: false

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.TM_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./dist/jx3-wbl-tools.user.js
          asset_name: jx3-wbl-tools.user.js
          asset_content_type: application/javascript
