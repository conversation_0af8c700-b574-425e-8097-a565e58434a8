# Vue 3 + TypeScript + Vite + TemperMonkey

This template should help get you started developing with Vue 3 and TypeScript in TemperMonkey.

# Try it now!

## GitHub Template

[Create a repo from this template on GitHub](https://github.com/liuc-c/tampermonkey-vite-starter/generate).

## Clone to local

If you prefer to do it manually with the cleaner git history

```shell
npx degit liuc-c/tampermonkey-vite-starter my-tampermonkey
cd my-tampermonkey
pnpm i # If you don't have pnpm installed, run: npm install -g pnpm
```

## Checklist

When you use this template, try follow the checklist to update your info properly

- [ ] Change the author name in `LICENSE`
- [ ] Change the script info in `vite.config.ts`
    - change to your own name
    - change to your own icon
    - change to your own namespace
    - change to your own match
- [ ] Change the description,homepage,bugs,repository and author in `package.json`

# Thanks repo

[vite-plugin-monkey](https://github.com/lisonge/vite-plugin-monkey)

