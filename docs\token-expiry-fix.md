# Token过期问题修复方案

## 问题描述

在添加新账号并页面刷新后，当用户在多开管理中切换回其他账号时，这些账号的token直接过期了。

## 问题根因分析

1. **过期检查过于严格**：原有的token过期检查逻辑 `item.expire > Date.now()` 过于严格，没有考虑时间差和缓冲
2. **过期时间设置过短**：原来设置的过期时间只有1小时，容易在页面刷新后被误判为过期
3. **缺少状态初始化**：页面刷新后没有正确初始化token状态，导致有效的token被错误过滤
4. **缺少token有效性验证**：账号切换时没有验证目标token的有效性

## 解决方案

### 1. 优化token过期检查逻辑

**修改位置**: `src/compositions/useMultiOpen.ts` - `getLocalAccountTokens()` 函数

**核心改进**:
- 添加5分钟缓冲时间，避免时间差导致的误判
- 向后兼容没有过期时间的token
- 增加详细的日志输出便于调试

```typescript
function getLocalAccountTokens() {
  const list = getSessionStorage('accountTokenList', []) as AccountToken[]
  
  const currentTime = Date.now()
  const filterList = list.filter((item) => {
    // 如果没有过期时间，认为是有效的（向后兼容）
    if (!item.expire) return true
    
    // 添加5分钟的缓冲时间，避免因为时间差导致的误判
    const bufferTime = 5 * 60 * 1000 // 5分钟缓冲
    const isValid = item.expire > (currentTime - bufferTime)
    
    if (!isValid) {
      console.log(`账号 ${item.account} 的token已过期，过期时间: ${new Date(item.expire).toLocaleString()}, 当前时间: ${new Date(currentTime).toLocaleString()}`)
    }
    
    return isValid
  })
  
  if (filterList.length !== list.length) {
    console.log(`过滤掉 ${list.length - filterList.length} 个过期的账号token`)
    setSessionStorage('accountTokenList', filterList)
  }
  
  return filterList
}
```

### 2. 延长token过期时间

**修改位置**: `src/compositions/useMultiOpen.ts` - `login()` 函数

**改进内容**:
- 将过期时间从1小时延长到2小时
- 添加详细的日志记录

```typescript
// 设置更长的过期时间：2小时，避免频繁过期
const expireTime = Date.now() + 2 * 3600 * 1000 // 2小时

if (index !== -1) {
  accountTokenList.value[index].token = res.data.ts_session_id
  accountTokenList.value[index].expire = expireTime
  console.log(`更新账号 ${currLoginInfo.value.account} 的token，过期时间: ${new Date(expireTime).toLocaleString()}`)
} else {
  accountTokenList.value.push({
    account: currLoginInfo.value.account,
    token: res.data.ts_session_id,
    expire: expireTime,
  })
  console.log(`添加新账号 ${currLoginInfo.value.account}，过期时间: ${new Date(expireTime).toLocaleString()}`)
}
```

### 3. 添加token有效性检查工具函数

**新增函数**: `isTokenValid()` 和 `refreshTokenExpire()`

```typescript
/**
 * 检查token是否有效（未过期）
 */
export function isTokenValid(token: string): boolean {
  const account = accountTokenList.value.find(item => item.token === token)
  if (!account) return false
  
  // 如果没有过期时间，认为是有效的（向后兼容）
  if (!account.expire) return true
  
  // 添加5分钟的缓冲时间
  const bufferTime = 5 * 60 * 1000 // 5分钟缓冲
  const isValid = account.expire > (Date.now() - bufferTime)
  
  if (!isValid) {
    console.log(`账号 ${account.account} 的token已过期，过期时间: ${new Date(account.expire).toLocaleString()}`)
  }
  
  return isValid
}

/**
 * 刷新账号token的过期时间
 */
export function refreshTokenExpire(token: string, newExpireTime?: number): boolean {
  const index = accountTokenList.value.findIndex(item => item.token === token)
  if (index === -1) return false
  
  const expireTime = newExpireTime || (Date.now() + 2 * 3600 * 1000) // 默认2小时
  accountTokenList.value[index].expire = expireTime
  setSessionStorage('accountTokenList', accountTokenList.value)
  
  console.log(`刷新账号 ${accountTokenList.value[index].account} 的token过期时间: ${new Date(expireTime).toLocaleString()}`)
  return true
}
```

### 4. 添加token状态初始化

**新增函数**: `initializeTokenStates()`

```typescript
/**
 * 初始化token状态，在页面加载时调用
 * 确保所有token状态正确，避免页面刷新后token丢失
 */
export function initializeTokenStates() {
  try {
    console.log('初始化token状态...')
    
    // 重新加载账号列表，应用过期检查
    const validAccounts = getLocalAccountTokens()
    accountTokenList.value = validAccounts
    
    // 检查当前token是否有效
    const currentToken = getSessionStorage('currToken')
    const mainToken = getLocalStorage('token')
    
    if (currentToken && currentToken !== mainToken) {
      // 如果当前token是多开账号的token，检查其有效性
      if (!isTokenValid(currentToken)) {
        console.log('当前token已过期，切换回主账号')
        setSessionStorage('currToken', mainToken)
        
        // 更新当前账号显示
        const mainAccount = getSessionStorage('account', '主账号')
        currAccount.value = `账号：${mainAccount}`
        
        warningNotify('当前账号登录状态已过期，已切换回主账号')
      } else {
        // 刷新有效token的过期时间
        refreshTokenExpire(currentToken)
      }
    }
    
    console.log(`token状态初始化完成，有效账号数量: ${validAccounts.length}`)
  } catch (error) {
    console.error('初始化token状态失败:', error)
  }
}
```

### 5. 优化账号切换逻辑

**修改位置**: `src/compositions/useAccountSwitch.ts` - `switchAccountSafely()` 函数

**改进内容**:
- 在切换前检查目标token的有效性
- 自动刷新有效token的过期时间
- 提供清晰的错误提示

```typescript
// 检查目标token是否有效
if (targetToken && targetToken !== getLocalStorage('token')) {
  // 如果是多开账号，检查token有效性
  if (!isTokenValid(targetToken)) {
    const account = accountTokenList.value.find(item => item.token === targetToken)
    const accountName = account?.account || '未知账号'
    errNotify(`账号 ${accountName} 的登录状态已过期，请重新登录`)
    return false
  }
  
  // 刷新token过期时间
  refreshTokenExpire(targetToken)
}
```

### 6. 集成到应用初始化流程

**修改位置**: `src/compositions/useInit.ts`

```typescript
export async function useInit() {
  setCurrWeb()
  if (currWeb.value === 'wbl' || currWeb.value === 'm_wbl') {
    // 初始化token状态，确保多开账号状态正确
    initializeTokenStates()
    
    await setCurrRole()
    // ... 其他初始化逻辑
  }
}
```

## 修复效果

1. **解决token误判过期**：通过缓冲时间和更长的有效期，避免有效token被误判为过期
2. **自动状态恢复**：页面刷新后自动检查和恢复token状态
3. **智能切换验证**：账号切换时自动验证token有效性，提供清晰的错误提示
4. **向后兼容**：保持对旧版本数据的兼容性

## 测试验证

1. **添加新账号测试**：添加新账号后页面刷新，验证其他账号token仍然有效
2. **账号切换测试**：在多开管理中切换不同账号，验证切换成功且无过期问题
3. **长时间测试**：验证token在设定的2小时内保持有效
4. **边界情况测试**：测试临近过期时间的token处理

## 监控和调试

通过详细的控制台日志，可以监控：
- Token过期检查过程
- 账号状态初始化过程
- Token刷新操作
- 账号切换验证过程

这些日志有助于快速定位和解决相关问题。
