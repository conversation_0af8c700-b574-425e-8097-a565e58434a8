{"name": "jx3-wbl-tools", "type": "module", "version": "1.2.10", "private": true, "description": "万宝楼小助手", "author": "方仟仟", "license": "MIT", "homepage": "https://github.com/liuc-c/tampermonkey-scripts#readme", "repository": {"type": "git", "url": "git+https://github.com/liuc-c/tampermonkey-scripts.git"}, "bugs": {"url": "https://github.com/liuc-c/tampermonkey-scripts/issues"}, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build --minify esbuild", "release": "vite build --minify esbuild && tcb storage upload dist/ qianqian/ -e script-login-7gl4kl3kda68f130 -r gz", "preview": "vite preview", "lint": "eslint .", "test": "vitest", "test:unit": "vitest", "typecheck": "vue-tsc --noEmit", "up": "taze major -I"}, "dependencies": {"canvas-confetti": "^1.9.3", "jsencrypt": "^3.3.2", "qrcode": "^1.5.4", "quasar": "^2.18.1", "vue": "^3.5.17"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@iconify/json": "^2.2.351", "@quasar/vite-plugin": "^1.9.0", "@types/node": "^20.19.1", "@unocss/eslint-config": "^0.59.4", "@unocss/reset": "^0.59.4", "@vitejs/plugin-vue": "^5.2.4", "@vue/compiler-sfc": "^3.5.17", "@vueuse/core": "^10.11.1", "eslint": "^9.29.0", "eslint-plugin-format": "^0.1.3", "eslint-ts-patch": "^8.57.0-0", "taze": "^0.13.9", "typescript": "^5.8.3", "unocss": "^0.59.4", "vite": "^5.4.19", "vite-plugin-monkey": "^3.5.2", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.1", "vue-tsc": "^2.2.10"}, "lint-staged": {"*": "eslint --fix"}}