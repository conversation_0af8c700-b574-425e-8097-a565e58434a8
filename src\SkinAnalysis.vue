<script setup lang="ts">
import { robTableLoading } from '~/compositions/useRob'
import type { TableColumns } from '~/type'

interface SkinAnalysisType {
  name: string
  type: string
  sum: number
  sale_num: number
  public_num: number
  sold_num: number
  update_time: number
}

const columns: TableColumns<SkinAnalysisType>[] = [
  { name: 'name', label: '外观', align: 'left', field: 'name' },
  { name: 'type', label: '类型', align: 'center', field: 'type' },
  { name: 'sum', label: '总量', field: 'sum', sortable: true },
  { name: 'sale_num', label: '在售期数量', field: 'sale_num', sortable: true },
  { name: 'public_num', label: '公示期数量', field: 'public_num', sortable: true },
  { name: 'sold_num', label: '今日售出数量', field: 'sold_num', sortable: true },
  { name: 'update_time', label: '更新时间', field: 'update_time', sortable: true },
]

const rows: SkinAnalysisType[] = [
  {
    name: 'string',
    type: 'string',
    sum: 0,
    sale_num: 0,
    public_num: 0,
    sold_num: 0,
    update_time: 0,
  },
]
</script>

<template>
  <div>
    画饼ing
    <q-table
      title="Treats"
      :rows="rows"
      :columns="columns"
      :loading="robTableLoading"
      :pagination="{
        rowsPerPage: 10,
      }"
      :rows-per-page-options="[10]"
    />
  </div>
</template>

<style scoped>

</style>
