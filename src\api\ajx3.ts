import { GM_xmlhttpRequest } from '$'

export function getAjx3AccountDetailsApi(consignment_id: string) {
  return new Promise((resolve, reject) => {
    const getIdUrl = `https://www.aijx3.cn/api/web/getZhanghaoResultWbl3?notNeedString=%2C&fangAnName=&needString=%2C&keyWord=&accoSeq=${consignment_id}&zhenYing=&accountType=&orderMode=&detailType=1&tradeType=1&tradeStatus=&accountDay=&searchData=%2C&page=1&size=20&orderBy=`
    GM_xmlhttpRequest({
      method: 'GET',
      url: getIdUrl,
      onload(response) {
        resolve(JSON.parse(response.responseText))
      },
      onerror(error) {
        reject(error)
      },
    })
  })
}

export function getAjx3GoodsDetailsApi(consignment_id: string) {
  const url = `https://www.aijx3.cn/api/wblwg/record/queryByCondition`
  const body = { tradeStatus: '', accoSeq: consignment_id, searchId: [], current: 1, size: 10 }
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      method: 'POST',
      url,
      data: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
      onload(response) {
        resolve(JSON.parse(response.responseText))
      },
      onerror(error) {
        reject(error)
      },
    })
  })
}
