import { GM_xmlhttpRequest } from '$'

export function getJx3ShAccountDetailsApi(consignment_id: string) {
  return new Promise((resolve, reject) => {
    const getIdUrl = `https://www.jx3sh.com/api/search/goods`
    const body = {
      server_area_index: [-1, -1, -1],
      is_search: 0,
      page: 1,
      limit: 20,
      type: 'wbl',
      status: 0,
      sort_field: 'time',
      sort_type: 'asc',
      keyword: consignment_id,
    }
    GM_xmlhttpRequest({
      method: 'POST',
      url: getIdUrl,
      data: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
      onload(response) {
        resolve(JSON.parse(response.responseText))
      },
      onerror(error) {
        reject(error)
      },
    })
  })
}

export async function getJx3ShAccountIDApi(id: string) {
  const res = await fetch(`https://www.jx3sh.com/api/search/detail?id=${id}}&type=wbl`, {
    headers: {
      'accept': 'application/json, text/plain, */*',
      'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'priority': 'u=1, i',
      'sec-ch-ua': '"Chromium";v="124", "Microsoft Edge";v="124", "Not-A.Brand";v="99"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'token': 'null',
      'uuid': 'null',
    },
    referrerPolicy: 'no-referrer',
    body: null,
    method: 'GET',
    mode: 'cors',
    credentials: 'omit',
  })
  return await res.json()
}
