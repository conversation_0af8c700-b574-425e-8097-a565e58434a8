import { http, httpWithErr } from '~/utils/request'
import { GMRequest } from '~/utils/GMRequest'

const baseUrl = 'https://trade-api.seasunwbl.com'
const gmRequest = new GMRequest()
const apis = {
  gateways: { url: '/m_api/platform/setting/gateways', type: 'GET' },
  roles: { url: '/m_api/passport/user/roles', type: 'GET' },
  select_role: { url: '/m_api/passport/user/select_role', type: 'GET' },
}

export function getGatewaysApi() {
  return http(`${baseUrl}${apis.gateways.url}?game_id=jx3`)
}

export function getRolesApi(serverId: string, token = '') {
  const url = `${baseUrl}${apis.roles.url}?game_id=jx3&server_id=${serverId}`
  if (token)
    return gmRequest.getByToken(url, token)
  else
    return httpWithErr(url)
}

export function selectRoleApi(serverId: string, roleId: string, token = '') {
  const url = `${baseUrl}${apis.select_role.url}?game_id=jx3&server_id=${serverId}&role_id=${roleId}`
  if (token)
    return gmRequest.getByToken(url, token)
  else
    return httpWithErr(url)
}
