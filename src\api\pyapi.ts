import { http } from '~/utils/request'

const baseUrl = 'https://py-api.seasunwbl.com/ws/jx3/wgtj'
function createQueryParams(zone: string, server: string, role: string) {
  const params = {
    categories: [{ category1: '外观礼盒', category2: '包身', category3: '' }, {
      category1: '外观礼盒',
      category2: '七夕',
      category3: '',
    }, { category1: '外观礼盒', category2: '中秋', category3: '' }, {
      category1: '外观礼盒',
      category2: '冠军',
      category3: '',
    }, { category1: '外观礼盒', category2: '联动', category3: '' }, {
      category1: '外观礼盒',
      category2: '妙音大唐',
      category3: '',
    }, { category1: '外观礼盒', category2: '同人', category3: '' }, {
      category1: '发型',
      category2: '金发',
      category3: '',
    }, { category1: '发型', category2: '红发', category3: '' }, {
      category1: '发型',
      category2: '异色发',
      category3: '',
    }, { category1: '外装', category2: '天选计划', category3: '' }, {
      category1: '外装',
      category2: '复刻',
      category3: '',
    }, { category1: '外装', category2: '特效', category3: '' }, {
      category1: '坐骑',
      category2: '骏马礼盒',
      category3: '',
    }, { category1: '披风', category2: '特效', category3: '' }, {
      category1: '挂宠',
      category2: '不限',
      category3: '',
    }, { category1: '发型', category2: '白发', category3: '' }, {
      category1: '坐骑',
      category2: '马具礼盒',
      category3: '',
    }, { category1: '坐骑', category2: '特效坐骑', category3: '' }],
    zone,
    server,
    role,
  }
  const body = { uri: '/external-q/jx3/appearance-handbook/appearance/summary', params: JSON.stringify(params) }
  return JSON.stringify(body)
}

/**
 * 获取当前角色信息
 * @param zone 大区
 * @param server 服务器
 * @param role 角色
 */
export function getCurrRoleInfoApi(zone: string, server: string, role: string) {
  return http(`${baseUrl}/proxy`, { method: 'POST', body: createQueryParams(zone, server, role) })
}

export function loginByToken(token: string) {
  return http(`${baseUrl}/login_by_token?token=${token}`)
}
