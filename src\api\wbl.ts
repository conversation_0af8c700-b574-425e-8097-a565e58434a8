import { http, httpWithErr, httpWithSession } from '~/utils/request'
import { getLocalStorage, getReqId, getSessionStorage, getTs } from '~/utils/utils'
import { GM_xmlhttpRequest } from '$'
import type {
  AdditionalService,
  AdditionalServiceOptions,
  ConsigneeInfo,
  DeliveryInfo,
  GatewaysResponse,
  GeetestObj,
  GoodsDetail,
  OrderInfo,
  ServiceFeeInfo,
  UserRolesResponse,
  baseRoleInfo,
} from '~/type'
import type { GoodsState } from '~/api/enums'
import { GOODS_STATE_MAPPING } from '~/api/enums'
import { GMRequest } from '~/utils/GMRequest'

const baseUrl = 'https://trade-api.seasunwbl.com'
const apis = {
  // 前置验证
  pre_auth: { url: `${baseUrl}/api/platform/captcha/pre_auth`, type: 'GET' },
  // 我的关注列表
  follow_list: { url: `${baseUrl}/api/passport/follow/list`, type: 'GET' },
  // 获取商品详细信息
  detail_info: { url: `${baseUrl}/api/buyer/goods/detail`, type: 'GET' },
  // 创建订单
  create_order: { url: `${baseUrl}/api/buyer/order/create`, type: 'POST' },
  // 支付
  pay: { url: `${baseUrl}/api/buyer/order/pay`, type: 'GET' },
  // 获取附加服务费
  additional_service: { url: `${baseUrl}/api/buyer/goods/additional_service`, type: 'GET' },
  // 获取交易数据
  trade_data: { url: `${baseUrl}/api/buyer/goods/trade_data`, type: 'GET' },
  // 添加关注
  follow_add: { url: `${baseUrl}/api/passport/follow/add`, type: 'GET' },
  // 取消关注
  cancel_follow: { url: `${baseUrl}/api/passport/follow/cancel`, type: 'GET' },
  // 获取商品列表
  goods_list: { url: `${baseUrl}/api/buyer/goods/list`, type: 'GET' },
  // 获取订单状态
  order_status: { url: `${baseUrl}/api/buyer/order/status`, type: 'GET' },
  // 基础信息
  base_info: { url: `${baseUrl}/api/passport/user/base_info`, type: 'GET' },
  // 商品基础信息
  appearance_base_info: { url: `${baseUrl}/api/buyer/goods/additional_data`, type: 'GET' },
  // 外观汇总数据
  summary_data: { url: `${baseUrl}/api/buyer/goods/summary_data`, type: 'GET' },
  // 订单列表
  order_list: { url: `${baseUrl}/api/buyer/order/list`, type: 'GET' },
  // get_appearance_map_token
  get_appearance_map_token: { url: `${baseUrl}/api/passport/user/get_appearance_map_token`, type: 'GET' },
  // 取消订单
  cancel_order: { url: `${baseUrl}/api/buyer/order/cancel`, type: 'GET' },
  // 登录
  login: { url: `${baseUrl}/api/passport/user/web_login`, type: 'GET' },
  // 获取区服信息
  gateways: { url: `${baseUrl}/api/platform/setting/gateways`, type: 'GET' },
  // 登出
  logout: { url: `${baseUrl}/api/passport/user/logout`, type: 'GET' },
  // 获取用户角色列表
  user_roles: { url: `${baseUrl}/api/passport/user/roles`, type: 'GET' },
}
const gmRequest = new GMRequest()
export function getCaptchaPreAuthApi() {
  return http(`${apis.pre_auth.url}?version=v5&__ts__=${getTs()}`)
}

export function logoutApi(token: string) {
  return gmRequest.getByToken(`${apis.logout.url}?req_id=${getReqId()}&game_id=jx3&__ts__=${getTs()}`, token)
}

interface ResponseData {
  config: any
}

interface Response {
  data: ResponseData
}

export function checkServerIsOpenApi() {
  return httpWithErr(`${apis.base_info.url}?req_id=${getReqId()}&game_id=jx3&__ts__=${getTs()}`)
}

/**
 * 获取登录验证码
 */
export function getLoginCaptchaPreAuthApi() {
  return new Promise<Response>((resolve, reject) => {
    GM_xmlhttpRequest({
      method: 'GET',
      url: `https://pf-api.xoyo.com/passport/common_api/pre_auth?version=v5&__ts__=${getTs()}`,
      onload(response) {
        resolve(JSON.parse(response.responseText))
      },
      onerror(error) {
        reject(error)
      },
    })
  })
}

export async function loginApi(account: string, password: string, captcha: GeetestObj, credentials: 'omit' | 'include' = 'include') {
  const url = `${apis.login.url}?req_id=${getReqId()}&game_id=jx3&account=${account}&password=${password}&encrypt_method=rsa&geetest_ctype=web&captcha_id=${captcha?.captcha_id}&lot_number=${captcha?.lot_number}&pass_token=${captcha?.pass_token}&gen_time=${captcha?.gen_time}&captcha_output=${encodeURIComponent(captcha?.captcha_output || '')}&__ts__=${getTs()}`
  const res = await fetch(url, {
    method: 'GET',
    credentials,
    headers: {
      'Content-Type': 'application/json',
    },
  })
  return res.json()
}

/**
 * 获取我的关注列表
 */
export function getMyFollowList() {
  return http(`${apis.follow_list.url}?req_id=${getReqId()}&game_id=jx3&__ts__=${getTs()}`)
}

/**
 * 取消关注
 * @param id 商品id
 * @param goods_type 商品类型 2:角色 3:外观
 */
export function cancelFollowApi(id: string, goods_type: number) {
  return http(`${apis.cancel_follow.url}?req_id=${getReqId()}&game_id=jx3&consignment_id_list%5B0%5D=${id}&goods_type=${goods_type}&__ts__=${getTs()}`)
}

/**
 * 添加关注
 * @param id 商品id
 * @param goods_type 商品类型 2:角色 3:外观
 */
export function addFollowApi(id: string, goods_type: number) {
  return http(`${apis.follow_add.url}?req_id=${getReqId()}&game_id=jx3&consignment_id=${id}&type=${goods_type}&__ts__=${getTs()}`)
}

/**
 * 添加关注
 * @param id 商品id
 * @param goods_type 商品类型 2:角色 3:外观
 * @param token token
 */
export function addFollowApiByToken(id: string, goods_type: number, token: string) {
  const url = `${apis.follow_add.url}?req_id=${getReqId()}&game_id=jx3&consignment_id=${id}&type=${goods_type}&__ts__=${getTs()}`
  return gmRequest.getByToken(url, token)
}

/**
 * 添加关注
 * @param id 商品id
 * @param goods_type 商品类型 2:角色 3:外观
 */
export function addFollowWithErrApi(id: string, goods_type: number) {
  return httpWithErr(`${apis.follow_add.url}?req_id=${getReqId()}&game_id=jx3&consignment_id=${id}&type=${goods_type}&__ts__=${getTs()}`)
}

function createOrderByGm(body: string, token: string) {
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      method: 'POST',
      url: apis.create_order.url,
      cookie: `m_gray_switch=1; m_gray_switch_=1; ts_session_id=${token}; ts_session_id_=${token}`,
      data: body,
      headers: {
        'Content-Type': 'application/json',
      },
      onload(response) {
        resolve(JSON.parse(response.responseText))
      },
      onerror(error) {
        reject(error)
      },
    })
  })
}

/**
 * 创建订单
 * @param row 商品详细信息
 * @param geetestObj 验证码对象
 * @param type 商品类型 2:角色 3:外观
 * @param additionalServiceSum 附加服务费
 * @param transferServiceObj 附加服务费对象
 * @param additionalService 附加服务列表
 * @param token 多开时需要传token
 * @param deliveryInfo 收货地址信息（当type=3时使用）
 */
export function createOrderApi(row: GoodsDetail, geetestObj: GeetestObj, type: number, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, token = '', deliveryInfo?: DeliveryInfo) {
  const service_fee_info: ServiceFeeInfo = {
    separation_service_fee: 0,
    transfer_service_fee: 0,
  }

  // 根据商品类型和收货地址信息设置 consignee_info
  const consignee_info: ConsigneeInfo = {
    zone_id: row.zone_id,
    server_id: row.server_id,
  }

  // 当 type=3（购买外观）且提供了收货地址信息时，设置收货方式
  if (type === 3 && deliveryInfo) {
    consignee_info.delivery_destination = deliveryInfo.delivery_destination

    if (deliveryInfo.delivery_destination === 1) {
      // 游戏角色收货
      consignee_info.role_id = deliveryInfo.role_id
      consignee_info.zone_id = deliveryInfo.zone_id || row.zone_id
      consignee_info.server_id = deliveryInfo.server_id || row.server_id
    }
    // delivery_destination === 2 时为万宝楼收货，使用默认的 zone_id 和 server_id
  }
  let role_transfer_agm = 0
  if (type === 2) {
    additionalService?.filter(item => item.selected).forEach((item) => {
      if (item.name === 'transfer_service') {
        role_transfer_agm = 1
        consignee_info.zone_id = transferServiceObj?.zone_id || row.zone_id
        consignee_info.server_id = transferServiceObj?.server_id || row.server_id
        service_fee_info.transfer_service_fee = transferServiceObj?.value || 0
      }
      else if (item.name === 'separation_service') {
        service_fee_info.separation_service_fee = item.value
      }
      else if (item.name === 'change_group_service') {
        consignee_info.is_change_group = 1
        service_fee_info.change_group_service_fee = item.value
      }
    })
  }
  let baseInfo
  if (token === '')
    baseInfo = getSessionStorage('baseInfo') as baseRoleInfo
  const orderInfo: OrderInfo = {
    req_id: getReqId(),
    account_uid: baseInfo?.account_uid || '',
    game_id: 'jx3',
    buy_type: 0,
    total_price: row.single_unit_price + additionalServiceSum,
    total_quantity: 1,
    total_unit_count: 1,
    order_type: type,
    service_fee_info,
    consignee_info,
    list: [
      {
        count: 1,
        id: row.consignment_id,
      },
    ],
    ...geetestObj,
  }
  if (token !== '' || orderInfo.account_uid === '')
    delete orderInfo.account_uid

  if (role_transfer_agm === 1)
    orderInfo.role_transfer_agm = role_transfer_agm
  if (token) {
    return createOrderByGm(JSON.stringify(orderInfo), token)
  }
  else {
    return httpWithSession(apis.create_order.url, {
      method: apis.create_order.type,
      body: JSON.stringify(orderInfo),
    })
  }
}

/**
 * 获取详细信息
 * @param consignment_id 商品id
 * @param type 商品类型 2:角色 3:外观
 */
export function getDetailInfoApi(consignment_id: string, type: number) {
  return http(`${apis.detail_info.url}?req_id=${getReqId()}&consignment_id=${consignment_id}&goods_type=${type}`)
}

/**
 * 获取附加服务（角色分离费）
 * @param consignment_id 商品id
 * @param token 多开时需要传token
 */
export function getAdditionalServiceApi(consignment_id: string, token = '') {
  const url = `${apis.additional_service.url}?req_id=${getReqId()}&game_id=jx3&consignment_id=${consignment_id}&goods_type=2&__ts__=${getTs()}`
  if (token)
    return gmRequest.getByToken(url, token)
  else
    return http(url)
}

export function payApi(orderId: string, type: number, token = '') {
  const url = `${apis.pay.url}?req_id=${getReqId()}&game_id=jx3&order_id=${orderId}&pay_way_code=alipay_qr&order_type=${type}&__ts__=${getTs()}`
  if (token)
    return gmRequest.getByToken(url, token)
  else
    return httpWithErr(url)
}

/**
 * 获取订单状态
 * @param orderId 订单id
 * @param orderType 订单类型 2:角色 3:外观
 */
export function getOrderStatusApi(orderId: string, orderType: number) {
  const url = `${apis.order_status.url}?req_id=${getReqId()}&game_id=jx3&order_id=${orderId}&order_type=${orderType}&__ts__=${getTs()}`
  return http(url)
}

interface Sort {
  str: 'followed_num' | 'price'
  // 0:降序 1:升序
  num: 0 | 1
}

/**
 * 创建获取商品列表参数
 * @param goodsState 商品状态，2:在售 0:全部 1:公示
 * @param goodsName 商品名称
 * @param page 页码
 * @param sort 排序 { str: 排序字段, num: 排序方式 } 0:降序 1:升序 str: followed_num:关注数 price:价格
 */
function createQueryParams(goodsState: GoodsState, goodsName: string, page: number, sort: Sort) {
  const state = GOODS_STATE_MAPPING[goodsState]
  return {
    'req_id': getReqId(),
    'game_id': 'jx3',
    'filter[state]': state.toString(),
    'filter[price]': '0',
    'filter[role_appearance]': goodsName || '',
    'game': 'jx3',
    'page': page.toString(),
    'size': '10',
    'goods_type': '3',
    [`sort[${sort.str}]`]: sort.num.toString(),
    '__ts__': getTs().toString(),
  }
}

/**
 * 获取商品列表
 * @param goodsState 商品状态 2:在售 0:全部 1:公示
 * @param goodsName 商品名称
 * @param page 页码
 * @param sort 排序 { str: 排序字段, num: 排序方式 } 0:降序 1:升序 str: followed_num:关注数 price:价格
 */
export function getGoodsListApi(goodsState: GoodsState, goodsName: string, page: number, sort: Sort = { str: 'followed_num', num: 0 }) {
  const queryParams = createQueryParams(goodsState, goodsName, page, sort)
  const queryString = `?${new URLSearchParams(queryParams).toString()}`
  return http(`${apis.goods_list.url}${queryString}`)
}

export function getFollowByPointListApi(appearance_type: string, page: number) {
  const params = {
    'req_id': getReqId(),
    'game_id': 'jx3',
    'filter[state]': '0',
    'filter[price]': '0',
    'filter[appearance_type]': appearance_type,
    'filter[role_appearance]': '',
    'game': 'jx3',
    'page': page.toString(),
    'size': '10',
    'goods_type': '3',
    'sort[price]': '1',
    '__ts__': getTs().toString(),
  }
  const queryString = `?${new URLSearchParams(params).toString()}`
  return http(`${apis.goods_list.url}${queryString}`)
}

/**
 * 获取交易数据
 * @param consignment_id 商品id
 */
export function getTradeDataApi(consignment_id: string) {
  return http(`${apis.trade_data.url}?req_id=${getReqId()}&consignment_id=${consignment_id}&game_id=jx3&__ts__=${getTs()}`)
}

/**
 * 获取基础信息
 */
export function getBaseInfoApi(token = '', preserveMainCookie = false) {
  const url = `${apis.base_info.url}?req_id=${getReqId()}&game_id=jx3&__ts__=${getTs()}`
  if (token) {
    if (preserveMainCookie)
      return gmRequest.getByTokenSafe(url, token)
    else
      return gmRequest.getByToken(url, token)
  }
  else {
    return httpWithErr(url)
  }
}

/**
 * 获取基础信息（安全模式，用于账号切换，不会清理主要登录cookie）
 */
export function getBaseInfoApiSafe(token = '') {
  return getBaseInfoApi(token, true)
}

export async function setTokenApi() {
  await new Promise(resolve => setTimeout(resolve, 1000))
  const url = `${apis.base_info.url}?req_id=${getReqId()}&game_id=jx3&__ts__=${getTs()}`
  const token = getLocalStorage('token')
  if (token)
    await gmRequest.getByToken(url, token)
}

function getGoodsBaseInfoApi(consignment_id: string, additional_key: string, goods_type: number) {
  return http(`${apis.appearance_base_info.url}?goods_type=${goods_type}&additional_key=${additional_key}&req_id=${getReqId()}&game_id=jx3&__ts__=${getTs()}&consignment_id=${consignment_id}`)
}

/**
 * 获取外观基础信息
 * @param consignment_id 商品id
 */
export function getAppearanceBaseInfoApi(consignment_id: string) {
  return getGoodsBaseInfoApi(consignment_id, 'appearance_base_info', 3)
}

/**
 * 获取角色基础信息
 * @param consignment_id 商品id
 */
export function getRoleBaseInfoApi(consignment_id: string) {
  return getGoodsBaseInfoApi(consignment_id, 'role_base_info', 2)
}

export function getSummaryDataApi(consignment_id: string) {
  return http(`${apis.summary_data.url}?req_id=${getReqId()}a&game_id=jx3&consignment_id=${consignment_id}&goods_type=2&__ts__=${getTs()}`)
}

/**
 * 获取订单列表
 * @param page 页码
 * @param orderType 订单类型 2:角色 3:外观
 */
export function getOrderListApi(page: number, orderType: number) {
  return http(`${apis.order_list.url}?req_id=${getReqId()}&game_id=jx3&page=${page}&size=10&order_type=${orderType}&date_filter=5&__ts__=${getTs()}`)
}

/**
 * 获取外观地图token
 */
export function getAppearanceMapTokenApi() {
  return http(`${apis.get_appearance_map_token.url}`)
}

/**
 * 取消订单
 * @param orderId 订单id
 * @param orderType 订单类型 2:角色 3:外观
 */
export function cancelOrderApi(orderId: string, orderType: number) {
  return http(`${apis.cancel_order.url}?req_id=${getReqId()}&game_id=jx3&order_id=${orderId}&order_type=${orderType}&__ts__=${getTs()}`)
}

/**
 * 获取用户游戏角色列表
 * @param server_id 服务器ID
 * @param token 多开时需要传token
 */
export function getUserRolesApi(server_id: string, token = ''): Promise<UserRolesResponse> {
  const url = `${apis.user_roles.url}?req_id=${getReqId()}&game_id=jx3&server_id=${server_id}&__ts__=${getTs()}`
  if (token)
    return gmRequest.getByToken(url, token)
  else
    return http(url)
}

/**
 * 获取区服信息
 */
export function getGatewaysApi(): Promise<GatewaysResponse> {
  return http(`${apis.gateways.url}?game_id=jx3`)
}
