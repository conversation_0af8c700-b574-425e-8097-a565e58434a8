<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAccountSwitch } from '~/compositions/useAccountSwitch'
import { accountTokenList, currAccount } from '~/compositions/useMultiOpen'
import { getSessionStorage } from '~/utils/utils'

const { switchAccountSafely, isSwitching, switchProgress } = useAccountSwitch()

// 测试状态
const testResults = ref<Array<{
  test: string
  status: 'pending' | 'success' | 'error'
  message: string
  timestamp: number
}>>([])

const currentAccountInfo = ref('')
const switchCount = ref(0)

// 监听账号切换事件
function handleAccountSwitched(event: CustomEvent) {
  const detail = event.detail
  addTestResult('账号切换事件', 'success', `收到账号切换事件: ${detail.newAccount}`)
  updateCurrentAccountInfo()
}

// 更新当前账号信息
function updateCurrentAccountInfo() {
  const baseInfo = getSessionStorage('baseInfo')
  const currToken = getSessionStorage('currToken')
  currentAccountInfo.value = `当前账号: ${baseInfo?.account || '未知'} | Token: ${currToken?.substring(0, 8)}...`
}

// 添加测试结果
function addTestResult(test: string, status: 'pending' | 'success' | 'error', message: string) {
  testResults.value.unshift({
    test,
    status,
    message,
    timestamp: Date.now()
  })
  
  // 只保留最近20条记录
  if (testResults.value.length > 20) {
    testResults.value = testResults.value.slice(0, 20)
  }
}

// 测试账号切换
async function testAccountSwitch(token?: string) {
  const targetAccount = token 
    ? accountTokenList.value.find(item => item.token === token)?.account || '未知账号'
    : '主账号'
    
  addTestResult('账号切换测试', 'pending', `开始切换到: ${targetAccount}`)
  
  try {
    const success = await switchAccountSafely(token)
    
    if (success) {
      switchCount.value++
      addTestResult('账号切换测试', 'success', `成功切换到: ${targetAccount}`)
    } else {
      addTestResult('账号切换测试', 'error', `切换失败: ${targetAccount}`)
    }
  } catch (error) {
    addTestResult('账号切换测试', 'error', `切换异常: ${error.message}`)
  }
}

// 清理测试结果
function clearResults() {
  testResults.value = []
  switchCount.value = 0
}

// 格式化时间
function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString()
}

onMounted(() => {
  window.addEventListener('accountSwitched', handleAccountSwitched)
  updateCurrentAccountInfo()
  addTestResult('测试初始化', 'success', '账号切换测试组件已加载')
})

onUnmounted(() => {
  window.removeEventListener('accountSwitched', handleAccountSwitched)
})
</script>

<template>
  <q-card class="account-switch-test">
    <q-card-section>
      <div class="text-h6 q-mb-md">
        <q-icon name="i-pixelarticons-device-phone" class="q-mr-sm" />
        账号切换测试工具
      </div>
      
      <!-- 当前状态 -->
      <div class="status-section q-mb-md">
        <div class="text-subtitle2 q-mb-sm">当前状态</div>
        <div class="status-info">
          <div>{{ currentAccountInfo }}</div>
          <div>当前显示: {{ currAccount }}</div>
          <div>切换次数: {{ switchCount }}</div>
          <div v-if="isSwitching" class="text-orange">
            <q-spinner color="orange" size="sm" class="q-mr-sm" />
            {{ switchProgress || '正在切换...' }}
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions-section q-mb-md">
        <div class="text-subtitle2 q-mb-sm">测试操作</div>
        <div class="q-gutter-sm">
          <q-btn 
            color="primary" 
            label="切换到主账号" 
            :disable="isSwitching"
            @click="testAccountSwitch()"
          />
          <q-btn 
            v-for="account in accountTokenList" 
            :key="account.token"
            color="secondary" 
            :label="`切换到 ${account.account}`"
            :disable="isSwitching"
            @click="testAccountSwitch(account.token)"
          />
          <q-btn 
            color="grey" 
            label="清理结果" 
            flat
            @click="clearResults"
          />
        </div>
      </div>
      
      <!-- 测试结果 -->
      <div class="results-section">
        <div class="text-subtitle2 q-mb-sm">测试结果</div>
        <q-list bordered separator class="rounded-borders">
          <q-item v-if="testResults.length === 0">
            <q-item-section>
              <q-item-label class="text-grey-7">暂无测试结果</q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item v-for="result in testResults" :key="result.timestamp">
            <q-item-section avatar>
              <q-icon 
                :name="result.status === 'success' ? 'i-pixelarticons-check' : 
                      result.status === 'error' ? 'i-pixelarticons-close' : 
                      'i-pixelarticons-clock'"
                :color="result.status === 'success' ? 'green' : 
                       result.status === 'error' ? 'red' : 
                       'orange'"
              />
            </q-item-section>
            
            <q-item-section>
              <q-item-label>{{ result.test }}</q-item-label>
              <q-item-label caption>{{ result.message }}</q-item-label>
            </q-item-section>
            
            <q-item-section side>
              <q-item-label caption>{{ formatTime(result.timestamp) }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-card-section>
  </q-card>
</template>

<style scoped>
.account-switch-test {
  max-width: 800px;
  margin: 0 auto;
}

.status-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #2196f3;
}

.status-info {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
}

.actions-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
}

.results-section {
  max-height: 400px;
  overflow-y: auto;
}

.q-item {
  min-height: 60px;
}
</style>
