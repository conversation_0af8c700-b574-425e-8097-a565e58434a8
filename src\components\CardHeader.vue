<script setup lang="ts">
import IconBtn from '~/components/IconBtn.vue'

defineProps({ title: String })
const emit = defineEmits(['close'])
</script>

<template>
  <q-card-section class="row q-pb-none h-40px items-center">
    <div class="text-h7">
      {{ title }}
    </div>
    <q-space />
    <IconBtn
      icon="i-pixelarticons-close" color="red" class="absolute right-2 top-2" dense flat
      @click="emit('close')"
    />
  </q-card-section>
</template>

<style scoped>

</style>
