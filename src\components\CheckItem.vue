<script setup lang="ts">
defineProps({
  disabled: Boolean,
  title: String,
  subInfo: String,
  price: Number,
})
const selected = defineModel<boolean>()
</script>

<template>
  <q-item-section side top>
    <q-checkbox
      v-model="selected" :disable="disabled" checked-icon="i-pixelarticons-checkbox"
      unchecked-icon="i-pixelarticons-checkbox-on" color="teal"
    />
  </q-item-section>
  <q-item-section>
    <q-item-label>{{ title }} <span class="ml-2 text-red font-bold">{{ price || 0 }}</span></q-item-label>
    <q-item-label style="font-size: 12px" caption>
      {{ subInfo }}
    </q-item-label>
  </q-item-section>
</template>

<style scoped>

</style>
