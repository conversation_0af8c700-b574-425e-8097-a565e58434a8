<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'

import { getBaseInfoApi, getGatewaysApi, getUserRolesApi } from '~/api/wbl'
import { getLocalStorage, getSessionStorage, setLocalStorage, setSessionStorage } from '~/utils/utils'
import { errNotify } from '~/compositions/useNotify'
import { isSkinMultiOpen } from '~/compositions/useMultiOpen'
import type { DeliveryInfo, GatewayInfo, ServerInfo, UserRoleInfo, baseRoleInfo } from '~/type'

// 区服选择选项类型
interface ServerOption {
  label: string
  value: {
    zone_id: string
    server_id: string
    zone_name: string
    server_name: string
  }
}

interface Props {
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: DeliveryInfo | null): void
  (e: 'deliveryInfoChanged', info: DeliveryInfo | null): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits<Emits>()

// 状态变量
const loading = ref(false)
const loadingRoles = ref(false)
const baseInfo = ref<baseRoleInfo | null>(null)
const currentServerId = ref<string>('')
const currentZoneId = ref<string>('')
const roles = ref<UserRoleInfo[]>([])

// 区服相关状态
const allServers = ref<ServerOption[]>([])
const selectedServer = ref<ServerOption | null>(null)

// 从 localStorage 恢复用户偏好的收货方式
const savedDeliveryType = getLocalStorage('delivery_preference')
const deliveryType = ref<number>(savedDeliveryType ? Number(savedDeliveryType) : 2) // 默认选择万宝楼收货

// 监听多开抢外观模式，强制设置为万宝楼收货
watch(isSkinMultiOpen, (isMultiSkin) => {
  if (isMultiSkin) {
    deliveryType.value = 2 // 强制设置为万宝楼收货
    console.log('多开抢外观模式：已强制设置收获方式为万宝楼')
  }
}, { immediate: true })
const selectedRole = ref<UserRoleInfo | null>(null)
// @unocss-include
const deliveryOptions = [
  { label: '万宝楼收货', value: 2, icon: 'i-pixelarticons-home', description: '商品将发送到万宝楼，可随时提取' },
  { label: '游戏角色收货', value: 1, icon: 'i-pixelarticons-user', description: '商品将直接发送到指定游戏角色' },
]

// 初始化数据
onMounted(async () => {
  await initializeData()
})

// 获取用户基础信息和角色数据
async function initializeData() {
  loading.value = true
  try {
    // 1. 获取用户基础信息
    await loadBaseInfo()

    // 2. 如果有基础信息，获取区服ID并加载角色列表
    if (baseInfo.value) {
      await loadServerInfo()
      if (currentServerId.value) {
        await loadRoles()
        // 3. 自动预选当前用户的角色
        autoSelectCurrentRole()
      }
    }
  }
  catch (error) {
    console.error('初始化收货地址数据失败:', error)
    errNotify('获取用户信息失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}

// 获取用户基础信息
async function loadBaseInfo() {
  // 先尝试从缓存获取
  const cachedBaseInfo = getSessionStorage('baseInfo')
  if (cachedBaseInfo) {
    baseInfo.value = cachedBaseInfo
    return
  }

  // 缓存中没有，调用API获取
  try {
    const res = await getBaseInfoApi()
    if (res.code === 1 && res.data) {
      baseInfo.value = res.data
      // 缓存到 sessionStorage
      setSessionStorage('baseInfo', JSON.stringify(res.data))
      setSessionStorage('currToken', JSON.stringify(res.data.ts_session_id))
    }
  }
  catch (error) {
    console.error('获取基础信息失败:', error)
  }
}

// 获取区服信息并构建选项列表
async function loadServerInfo() {
  if (!baseInfo.value)
    return

  try {
    const res = await getGatewaysApi()
    if (res.code === 1 && res.data?.list) {
      // 构建所有区服选项
      const serverOptions: ServerOption[] = []
      let currentServerOption: ServerOption | null = null

      res.data.list.forEach((zone: GatewayInfo) => {
        zone.servers.forEach((server: ServerInfo) => {
          const option: ServerOption = {
            label: `${zone.zone_name}-${server.server_name}`,
            value: {
              zone_id: zone.zone_id,
              server_id: server.server_id,
              zone_name: zone.zone_name,
              server_name: server.server_name,
            },
          }
          serverOptions.push(option)

          // 检查是否是当前用户的区服
          if (zone.zone_name === baseInfo.value?.zone_name
            && server.server_name === baseInfo.value?.server_name) {
            currentServerOption = option
            currentZoneId.value = zone.zone_id
            currentServerId.value = server.server_id
          }
        })
      })

      allServers.value = serverOptions
      selectedServer.value = currentServerOption
    }
  }
  catch (error) {
    console.error('获取区服信息失败:', error)
  }
}

// 获取角色列表
async function loadRoles(serverId?: string) {
  const targetServerId = serverId || currentServerId.value
  if (!targetServerId)
    return

  loadingRoles.value = true
  try {
    const token = getLocalStorage('token') || ''
    const res = await getUserRolesApi(targetServerId, token)

    if (res.code === 1 && res.data?.list)
      roles.value = res.data.list.filter(role => role.freeze === 0) // 过滤掉冻结的角色
    else
      roles.value = []
  }
  catch (error) {
    console.error('获取角色列表失败:', error)
    roles.value = []
  }
  finally {
    loadingRoles.value = false
  }
}

// 自动预选当前用户的角色
function autoSelectCurrentRole() {
  if (!baseInfo.value?.role_name || roles.value.length === 0)
    return

  const currentRole = roles.value.find(role =>
    role.name === baseInfo.value?.role_name,
  )

  if (currentRole)
    selectedRole.value = currentRole
}

// 区服选择变化处理
async function onServerChange(server: ServerOption | null) {
  selectedServer.value = server
  selectedRole.value = null // 清空角色选择

  if (server) {
    // 更新当前区服信息
    currentZoneId.value = server.value.zone_id
    currentServerId.value = server.value.server_id

    // 加载新区服的角色列表
    await loadRoles(server.value.server_id)

    // 如果选择的是用户当前区服，自动预选当前角色
    if (server.value.zone_name === baseInfo.value?.zone_name
      && server.value.server_name === baseInfo.value?.server_name)
      autoSelectCurrentRole()
  }
  else {
    roles.value = []
  }
}

// 计算收货地址信息
const deliveryInfo = computed<DeliveryInfo | null>(() => {
  if (deliveryType.value === 2) {
    // 万宝楼收货
    return {
      delivery_destination: 2,
    }
  }
  else if (deliveryType.value === 1 && selectedRole.value && currentZoneId.value && currentServerId.value) {
    // 游戏角色收货
    return {
      delivery_destination: 1,
      role_id: selectedRole.value.id,
      zone_id: currentZoneId.value,
      server_id: currentServerId.value,
    }
  }
  return null
})

// 监听收货信息变化
watch(deliveryInfo, (newInfo) => {
  emit('update:modelValue', newInfo)
  emit('deliveryInfoChanged', newInfo)
}, { immediate: true })

// 监听收货方式变化，重置选择并保存用户偏好
watch(deliveryType, (newType) => {
  if (newType === 2) {
    selectedRole.value = null
    selectedServer.value = null
  }
  else if (newType === 1) {
    // 切换到游戏角色收货时，恢复默认区服选择
    if (allServers.value.length > 0 && baseInfo.value) {
      const defaultServer = allServers.value.find(server =>
        server.value.zone_name === baseInfo.value?.zone_name
        && server.value.server_name === baseInfo.value?.server_name,
      )
      if (defaultServer)
        onServerChange(defaultServer)
    }
  }

  // 保存用户偏好到 localStorage
  setLocalStorage('delivery_preference', newType.toString())
})

// 角色选择变化
function onRoleSelected(role: UserRoleInfo | null) {
  selectedRole.value = role
}

// 验证是否可以提交
const isValid = computed(() => {
  if (deliveryType.value === 2)
    return true // 万宝楼收货总是有效
  else if (deliveryType.value === 1)
    return selectedServer.value !== null && selectedRole.value !== null // 游戏角色收货需要选择区服和角色

  return false
})

defineExpose({
  isValid,
  deliveryInfo,
})
</script>

<template>
  <div class="delivery-address-selector">
    <!-- 多开抢外观模式提示 -->
    <div v-if="isSkinMultiOpen" class="q-mb-md q-pa-md bg-orange-1 rounded-borders">
      <div class="text-subtitle2 text-orange-8 q-mb-sm">
        <q-icon name="i-pixelarticons-info" class="q-mr-xs" />
        多开抢外观模式
      </div>
      <div class="text-body2 text-orange-7">
        多开抢外观时，收获方式已统一设置为"万宝楼收货"，无法更改。
        <br>
        这样可以确保所有账号的外观商品都能正确收货。
      </div>
    </div>

    <!-- 收货方式选择 -->
    <div class="q-mb-lg">
      <div class="text-subtitle2 q-mb-sm">
        收货方式
      </div>
      <q-option-group
        v-model="deliveryType"
        :options="deliveryOptions"
        :disable="props.disabled || loading || isSkinMultiOpen"
        color="teal"
        type="radio"
      >
        <template #label="opt">
          <div class="row items-center q-gutter-sm">
            <q-icon :name="opt.icon" size="sm" />
            <div>
              <div class="text-weight-medium">
                {{ opt.label }}
              </div>
              <div class="text-caption text-grey-6">
                {{ opt.description }}
              </div>
            </div>
          </div>
        </template>
      </q-option-group>
    </div>

    <!-- 区服和角色选择（仅在选择游戏角色收货时显示） -->
    <div v-if="deliveryType === 1" class="q-mb-md">
      <!-- 区服选择 -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">
          选择收货区服
        </div>
        <q-select
          v-model="selectedServer"
          :options="allServers"
          :loading="loading"
          :disable="props.disabled || loading"
          placeholder="请选择区服"
          option-label="label"
          clearable
          color="teal"
          @update:model-value="onServerChange"
        >
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
                <q-item-label caption>
                  {{ scope.opt.value.zone_name }} - {{ scope.opt.value.server_name }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </template>

          <template #selected>
            <span v-if="selectedServer">
              {{ selectedServer.label }}
            </span>
          </template>

          <template #no-option>
            <q-item>
              <q-item-section class="text-grey">
                暂无可用区服
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>

      <!-- 角色选择 -->
      <div v-if="selectedServer" class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">
          选择收货角色
        </div>
        <q-select
          v-model="selectedRole"
          :options="roles"
          :loading="loadingRoles"
          :disable="props.disabled || loading || loadingRoles"
          placeholder="请选择要接收商品的角色"
          clearable
          color="teal"
          @update:model-value="onRoleSelected"
        >
          <template #option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.name }} (Lv.{{ scope.opt.level }})</q-item-label>
                <q-item-label caption>
                  角色ID: {{ scope.opt.id }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </template>

          <template #selected>
            <span v-if="selectedRole">
              {{ selectedRole.name }} (Lv.{{ selectedRole.level }})
            </span>
          </template>

          <template #no-option>
            <q-item>
              <q-item-section class="text-grey">
                {{ loadingRoles ? '正在加载角色列表...' : '该区服没有角色' }}
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>

      <!-- 提示信息 -->
      <div v-if="deliveryType === 1 && !selectedRole" class="text-caption text-orange q-mt-xs">
        <q-icon name="i-pixelarticons-info" size="xs" class="q-mr-xs" />
        请选择区服和角色来接收外观商品
      </div>
    </div>

    <!-- 收货信息预览 -->
    <div v-if="deliveryInfo" class="delivery-preview q-pa-md bg-grey-1 rounded-borders">
      <div class="text-subtitle2 q-mb-sm">
        <q-icon name="i-pixelarticons-check" color="teal" class="q-mr-xs" />
        收货信息确认
      </div>
      <div class="text-body2">
        <div v-if="deliveryType === 2" class="text-teal">
          <q-icon name="i-pixelarticons-home" class="q-mr-xs" />
          商品将发送到万宝楼
          <div class="text-caption text-grey-6 q-mt-xs">
            区服：{{ baseInfo?.zone_name }} - {{ baseInfo?.server_name }}
          </div>
        </div>
        <div v-else-if="deliveryType === 1 && selectedRole && selectedServer" class="text-teal">
          <q-icon name="i-pixelarticons-user" class="q-mr-xs" />
          商品将发送到角色：{{ selectedRole.name }} (Lv.{{ selectedRole.level }})
          <div class="text-caption text-grey-6 q-mt-xs">
            区服：{{ selectedServer.value.zone_name }} - {{ selectedServer.value.server_name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.delivery-address-selector {
  min-width: 400px;
}

.delivery-preview {
  border-left: 4px solid var(--q-teal);
}

.q-option-group .q-radio {
  margin-bottom: 12px;
}
</style>
