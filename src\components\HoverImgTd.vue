<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
import { hoverShowImg } from '~/utils/hoverShowImg'
import type { FollowItem } from '~/type'
import { successNotify } from '~/compositions/useNotify'
import RoleSummaryData from '~/components/RoleSummaryData.vue'
import { useRoleSummary } from '~/compositions/useRoleSummary'

const props = defineProps<{ row: FollowItem }>()
function isShowHover() {
  return props.row.type === 3 && hoverShowImg.isSetHoverImg('', props.row.goods_name, props.row.goods_type_name)
}
const { copy, isSupported } = useClipboard()

function copyName() {
  if (isSupported.value) {
    copy(props.row.goods_name)
    successNotify(`商品名 [ ${props.row.goods_name} ] 复制成功`)
  }
}

function openSearch() {
  window.open(`https://jx3.seasunwbl.com/buyer?appearance_name=${props.row.goods_name}&t=skin`)
}
const { isShowRoleSummary, position, onMouseEnter, onMouseLeave } = useRoleSummary()
</script>

<template>
  <template v-if="row.type === 2">
    <q-td @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
      <q-img :src="row.goods_icon_url" height="32px" style="max-width: 62px" fit="contain" />
      <span> {{ row.goods_name }}</span>
      <RoleSummaryData :position="position" :is-show-role-summary="isShowRoleSummary" :role-summary-data="row.role_summary_data" />
    </q-td>
  </template>
  <template v-else-if="isShowHover()">
    <q-td
      cursor-pointer
      @mouseenter="hoverShowImg.hoverImgMouseEnter($event, row.goods_name, 14, 80)"
      @mouseleave="hoverShowImg.hoverImgMouseLeave()"
    >
      <q-img :src="row.goods_icon_url" height="32px" style="max-width: 62px" fit="contain" @click="openSearch" />
      <span @click="copyName"> {{ row.goods_name }}</span>
    </q-td>
  </template>
  <template v-else>
    <q-td>
      <q-img :src="row.goods_icon_url" height="32px" style="max-width: 62px" fit="contain" @click="openSearch" />
      <span :class="isSupported ? 'cursor-pointer' : ''" @click="copyName"> {{ row.goods_name }}</span>
    </q-td>
  </template>
</template>

<style scoped>

</style>
