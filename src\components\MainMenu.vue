<script setup lang="ts">
import { ref } from 'vue'
import RobDialog from '~/components/dialogs/rob/RobDialog.vue'
import IconBtn from '~/components/IconBtn.vue'
import { useRob } from '~/compositions/useRob'
import { newVersionText, useUpdate } from '~/compositions/useUpdate'
import { useConcern } from '~/compositions/useConcern'
import ConcernDialog from '~/components/dialogs/ConcernDialog.vue'
import { showCurrRole } from '~/compositions/useCurrRole'
import CurrRoleDialog from '~/components/dialogs/CurrRoleDialog.vue'
import { openGoodsOrder } from '~/compositions/useGoodsOrder'
import GoodsOrderDialog from '~/components/dialogs/GoodsOrderDialog.vue'
import GoodsDetailDialog from '~/components/dialogs/GoodsDetailDialog.vue'
import { isShowSettingDialog, useAutoHideMenu } from '~/compositions/useSetting'
import SettingDialog from '~/components/dialogs/SettingDialog.vue'
import { isMaintenance, openMaintenanceDialog } from '~/compositions/useMaintenance'
import MaintenanceDialog from '~/components/dialogs/MaintenanceDialog.vue'
import ZfbDialog from '~/components/dialogs/rob/ZfbDialog.vue'
import { currWeb } from '~/compositions/useInit'
import { aijx3FollowRole } from '~/compositions/useAijx3'
import { j3shFollowRole } from '~/compositions/useJx3Sh'

const { openRob } = useRob()
const { openUpdateWeb, currentVersion, updateBtnIsLoading } = useUpdate()

const { openConcernDialog } = useConcern()
const pathName = location.pathname
const isShowMainMenu = ref(!(pathName === '/role' || pathName === '/skin'))

const { currentClass, toggleMenu, isHideMenu } = useAutoHideMenu()
</script>

<template>
  <IconBtn
    v-if="$q.screen.xs"
    class="fixed right--10px bottom-100px"
    style=" border-top-right-radius: 0;border-bottom-right-radius: 0;"
    push
    :icon="isHideMenu ? 'i-pixelarticons-chevron-left' : 'i-pixelarticons-chevron-right'"
    color="green" @click="toggleMenu"
  />
  <div v-if="isShowMainMenu && (currWeb === 'wbl' || currWeb === 'm_wbl')" :class="currentClass" class="fixed bottom-100px right-50px flex flex-col gap-10px">
    <IconBtn
      v-if="!isMaintenance()"
      push tooltip="买买买" tooltip-position="left" icon="i-pixelarticons-cart" color="orange"
      @click="openRob"
    />
    <IconBtn
      v-if="!isMaintenance()"
      push tooltip="批量关注" tooltip-position="left" icon="i-pixelarticons-heart" color="red"
      @click="openConcernDialog"
    />
    <IconBtn
      v-if="!isMaintenance()"
      push tooltip="购买订单记录" tooltip-position="left" icon="i-pixelarticons-script-text" color="teal"
      @click="openGoodsOrder"
    />
    <IconBtn
      v-if="isMaintenance()"
      push tooltip="蹲开服设置" tooltip-position="left" icon="i-pixelarticons-clock" color="teal"
      @click="openMaintenanceDialog"
    />
    <IconBtn
      push tooltip="设置" tooltip-position="left" icon="i-pixelarticons-power" color="teal"
      @click="isShowSettingDialog = true"
    />
    <IconBtn
      v-if="!isMaintenance()"
      push tooltip="外观收藏评分" tooltip-position="left" icon="i-pixelarticons-user" color="teal"
      @click="showCurrRole"
    />
    <IconBtn
      :loading="updateBtnIsLoading"
      push :tooltip="`更新脚本，当前版本：${currentVersion}${newVersionText}`" tooltip-position="left" icon="i-pixelarticons-cloud-download" color="green"
      @click="openUpdateWeb"
    />
    <RobDialog />
    <ConcernDialog />
    <CurrRoleDialog />
    <GoodsOrderDialog />
    <GoodsDetailDialog />
    <SettingDialog />
    <MaintenanceDialog />
    <ZfbDialog />
  </div>
  <div v-else-if="currWeb !== ''" class="fixed bottom-100px right-50px flex flex-col gap-10px">
    <IconBtn
      v-if="currWeb === 'aj3'"
      push tooltip="添加到万宝楼关注" tooltip-position="left" icon="i-pixelarticons-heart" color="red"
      @click="aijx3FollowRole"
    />
    <IconBtn
      v-if="currWeb === 'j3sh'"
      push tooltip="添加到万宝楼关注" tooltip-position="left" icon="i-pixelarticons-heart" color="red"
      @click="j3shFollowRole"
    />
  </div>
</template>

<style scoped>
.slide-in-right {
  animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}
/* ----------------------------------------------
 * Generated by Animista on 2024-5-9 15:7:44
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-in-right
 * ----------------------------------------
 */
@keyframes slide-in-right {
  0% {
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-out-right {
  animation: slide-out-right 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
}
/* ----------------------------------------------
 * Generated by Animista on 2024-5-9 15:6:12
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-out-right
 * ----------------------------------------
 */
@keyframes slide-out-right {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(1000px);
    opacity: 0;
  }
}
</style>
