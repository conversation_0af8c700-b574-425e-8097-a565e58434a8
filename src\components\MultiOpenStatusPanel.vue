<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useConcurrencyControl } from '~/compositions/useConcurrencyControl'
import { useErrorRecovery } from '~/compositions/useErrorRecovery'
import { useAccountSwitch } from '~/compositions/useAccountSwitch'
import { accountTokenList, multiGeetestObj, isRoleMultiOpen, isSkinMultiOpen } from '~/compositions/useMultiOpen'

const { getConcurrencyStatus, activeRequests, queueLength } = useConcurrencyControl()
const { getRecoveryStatus } = useErrorRecovery()
const { isSwitching, switchProgress } = useAccountSwitch()

// 状态更新定时器
let statusTimer: NodeJS.Timeout | null = null

// 实时状态数据
const concurrencyStatus = ref(getConcurrencyStatus())
const recoveryStatus = ref(getRecoveryStatus())

// 计算属性
const totalAccounts = computed(() => accountTokenList.value.length + 1) // +1 for main account
const validAccounts = computed(() => {
  const now = Date.now()
  return accountTokenList.value.filter(account => account.expire > now).length + 1
})

const captchaStatus = computed(() => {
  const required = totalAccounts.value
  const current = multiGeetestObj.value.length
  return {
    current,
    required,
    percentage: required > 0 ? Math.round((current / required) * 100) : 0,
    isComplete: current >= required
  }
})

const multiOpenMode = computed(() => {
  if (isRoleMultiOpen.value) return '多开抢号'
  if (isSkinMultiOpen.value) return '多开抢外观'
  return '未启用'
})

const networkHealth = computed(() => {
  const stats = concurrencyStatus.value.stats
  if (stats.total === 0) return { status: 'unknown', color: 'grey', text: '未知' }
  
  const successRate = stats.success / stats.total
  if (successRate >= 0.9) return { status: 'excellent', color: 'green', text: '优秀' }
  if (successRate >= 0.7) return { status: 'good', color: 'light-green', text: '良好' }
  if (successRate >= 0.5) return { status: 'fair', color: 'orange', text: '一般' }
  return { status: 'poor', color: 'red', text: '较差' }
})

// 更新状态数据
function updateStatus() {
  concurrencyStatus.value = getConcurrencyStatus()
  recoveryStatus.value = getRecoveryStatus()
}

// 格式化时间
function formatTime(ms: number): string {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

// 格式化百分比
function formatPercentage(value: number): string {
  return `${Math.round(value)}%`
}

onMounted(() => {
  // 每秒更新一次状态
  statusTimer = setInterval(updateStatus, 1000)
})

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
})
</script>

<template>
  <q-card class="multi-open-status-panel">
    <q-card-section>
      <div class="text-h6 q-mb-md flex items-center">
        <q-icon name="i-pixelarticons-dashboard" class="q-mr-sm" />
        多开抢号状态面板
        <q-space />
        <q-chip 
          :color="multiOpenMode === '未启用' ? 'grey' : 'teal'" 
          text-color="white" 
          size="sm"
        >
          {{ multiOpenMode }}
        </q-chip>
      </div>

      <!-- 账号状态 -->
      <div class="status-section q-mb-md">
        <div class="section-title">账号状态</div>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-label">总账号数</div>
            <div class="status-value">{{ totalAccounts }}</div>
          </div>
          <div class="status-item">
            <div class="status-label">有效账号</div>
            <div class="status-value text-green">{{ validAccounts }}</div>
          </div>
          <div class="status-item">
            <div class="status-label">账号切换</div>
            <div class="status-value">
              <q-spinner v-if="isSwitching" color="orange" size="sm" />
              <span v-else class="text-grey">待机</span>
            </div>
          </div>
        </div>
        <div v-if="isSwitching && switchProgress" class="switching-progress">
          <q-linear-progress indeterminate color="orange" class="q-mt-sm" />
          <div class="text-caption text-orange q-mt-xs">{{ switchProgress }}</div>
        </div>
      </div>

      <!-- 验证码状态 -->
      <div class="status-section q-mb-md">
        <div class="section-title">验证码状态</div>
        <div class="captcha-status">
          <div class="captcha-progress">
            <q-circular-progress
              :value="captchaStatus.percentage"
              size="60px"
              :thickness="0.15"
              :color="captchaStatus.isComplete ? 'green' : 'orange'"
              track-color="grey-3"
              class="q-mr-md"
            >
              <div class="text-caption">{{ captchaStatus.current }}/{{ captchaStatus.required }}</div>
            </q-circular-progress>
          </div>
          <div class="captcha-info">
            <div class="captcha-label">验证码完成度</div>
            <div class="captcha-percentage">{{ formatPercentage(captchaStatus.percentage) }}</div>
            <div class="captcha-status-text">
              <q-icon 
                :name="captchaStatus.isComplete ? 'i-pixelarticons-check' : 'i-pixelarticons-clock'" 
                :color="captchaStatus.isComplete ? 'green' : 'orange'"
                size="sm"
              />
              <span class="q-ml-xs">
                {{ captchaStatus.isComplete ? '验证码充足' : '需要更多验证码' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 并发控制状态 -->
      <div class="status-section q-mb-md">
        <div class="section-title">并发控制</div>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-label">活跃请求</div>
            <div class="status-value text-blue">{{ activeRequests }}</div>
          </div>
          <div class="status-item">
            <div class="status-label">队列长度</div>
            <div class="status-value">{{ queueLength }}</div>
          </div>
          <div class="status-item">
            <div class="status-label">动态延迟</div>
            <div class="status-value">{{ formatTime(concurrencyStatus.dynamicDelay) }}</div>
          </div>
        </div>
      </div>

      <!-- 网络状态 -->
      <div class="status-section q-mb-md">
        <div class="section-title">网络状态</div>
        <div class="network-status">
          <div class="network-health">
            <q-chip 
              :color="networkHealth.color" 
              text-color="white" 
              size="sm"
              class="q-mr-md"
            >
              {{ networkHealth.text }}
            </q-chip>
            <span class="text-caption">
              成功率: {{ formatPercentage((concurrencyStatus.stats.success / Math.max(concurrencyStatus.stats.total, 1)) * 100) }}
            </span>
          </div>
          <div class="network-metrics q-mt-sm">
            <div class="metric">
              <span class="metric-label">平均响应时间:</span>
              <span class="metric-value">{{ formatTime(concurrencyStatus.stats.avgResponseTime) }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">总请求数:</span>
              <span class="metric-value">{{ concurrencyStatus.stats.total }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误恢复状态 -->
      <div v-if="recoveryStatus.errorStats.totalErrors > 0" class="status-section">
        <div class="section-title">错误恢复</div>
        <div class="error-stats">
          <div class="error-summary">
            <span class="text-caption">
              总错误: {{ recoveryStatus.errorStats.totalErrors }} | 
              已恢复: {{ recoveryStatus.errorStats.recoveredErrors }} |
              恢复率: {{ formatPercentage((recoveryStatus.errorStats.recoveredErrors / recoveryStatus.errorStats.totalErrors) * 100) }}
            </span>
          </div>
          <div v-if="recoveryStatus.isRecovering" class="recovery-progress q-mt-sm">
            <q-linear-progress indeterminate color="orange" />
            <div class="text-caption text-orange q-mt-xs">正在进行错误恢复...</div>
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<style scoped>
.multi-open-status-panel {
  min-width: 350px;
  max-width: 500px;
}

.status-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
}

.section-title {
  font-weight: 600;
  font-size: 14px;
  color: #424242;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.status-item {
  text-align: center;
}

.status-label {
  font-size: 12px;
  color: #757575;
  margin-bottom: 4px;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  color: #424242;
}

.captcha-status {
  display: flex;
  align-items: center;
}

.captcha-info {
  flex: 1;
}

.captcha-label {
  font-size: 12px;
  color: #757575;
  margin-bottom: 4px;
}

.captcha-percentage {
  font-size: 18px;
  font-weight: 600;
  color: #424242;
  margin-bottom: 4px;
}

.captcha-status-text {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #757575;
}

.network-health {
  display: flex;
  align-items: center;
}

.network-metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.metric-label {
  color: #757575;
}

.metric-value {
  color: #424242;
  font-weight: 500;
}

.switching-progress {
  margin-top: 8px;
}

.error-summary {
  font-size: 12px;
  color: #757575;
}

.recovery-progress {
  margin-top: 8px;
}
</style>
