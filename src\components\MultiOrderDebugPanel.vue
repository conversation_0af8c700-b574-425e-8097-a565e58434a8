<template>
  <q-card class="debug-panel">
    <q-card-section>
      <div class="text-h6 q-mb-md">
        <q-icon name="i-pixelarticons-bug" class="q-mr-sm" />
        多账户订单调试面板
      </div>
      
      <!-- 状态概览 -->
      <div class="status-overview q-mb-md">
        <q-chip 
          :color="state.mainQrcodeGenerated ? 'green' : 'red'" 
          text-color="white" 
          icon="i-pixelarticons-qr-code"
        >
          主账号二维码: {{ state.mainQrcodeGenerated ? '已生成' : '未生成' }}
        </q-chip>
        
        <q-chip 
          :color="state.multiQrcodeGenerated > 0 ? 'green' : 'orange'" 
          text-color="white" 
          icon="i-pixelarticons-users"
        >
          多开二维码: {{ state.multiQrcodeGenerated }}/{{ state.accountCount }}
        </q-chip>
        
        <q-chip 
          :color="state.isolationActive ? 'orange' : 'green'" 
          text-color="white" 
          icon="i-pixelarticons-shield"
        >
          Cookie隔离: {{ state.isolationActive ? '激活' : '未激活' }}
        </q-chip>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions q-mb-md">
        <q-btn 
          color="primary" 
          icon="i-pixelarticons-refresh" 
          label="检查状态" 
          @click="refreshState"
          class="q-mr-sm"
        />
        <q-btn 
          color="secondary" 
          icon="i-pixelarticons-play" 
          label="开始监控" 
          @click="startMonitoring"
          :disable="isMonitoring"
          class="q-mr-sm"
        />
        <q-btn 
          color="warning" 
          icon="i-pixelarticons-stop" 
          label="停止监控" 
          @click="stopMonitoring"
          :disable="!isMonitoring"
          class="q-mr-sm"
        />
        <q-btn 
          color="info" 
          icon="i-pixelarticons-download" 
          label="导出日志" 
          @click="exportLogs"
        />
      </div>
      
      <!-- 验证结果 -->
      <div v-if="validationResult" class="validation-result q-mb-md">
        <q-banner 
          :class="validationResult.success ? 'bg-green-1 text-green-8' : 'bg-red-1 text-red-8'"
          :icon="validationResult.success ? 'i-pixelarticons-check' : 'i-pixelarticons-close'"
        >
          <div class="text-subtitle2">
            {{ validationResult.success ? '验证通过' : '发现问题' }}
          </div>
          <div v-if="!validationResult.success" class="q-mt-sm">
            <ul>
              <li v-for="issue in validationResult.issues" :key="issue">
                {{ issue }}
              </li>
            </ul>
          </div>
        </q-banner>
      </div>
      
      <!-- 调试日志 -->
      <div class="debug-logs">
        <div class="text-subtitle2 q-mb-sm">
          调试日志 ({{ logs.length }} 条)
          <q-btn 
            flat 
            dense 
            icon="i-pixelarticons-trash" 
            @click="clearLogs"
            class="q-ml-sm"
          />
        </div>
        
        <q-scroll-area style="height: 300px" class="bg-grey-1 q-pa-sm">
          <div v-for="log in logs" :key="log.timestamp" class="log-entry q-mb-xs">
            <q-chip 
              :color="getLogColor(log.level)" 
              text-color="white" 
              size="sm"
              class="q-mr-sm"
            >
              {{ log.level.toUpperCase() }}
            </q-chip>
            <span class="text-caption text-grey-7 q-mr-sm">
              {{ formatTime(log.timestamp) }}
            </span>
            <span class="text-weight-medium q-mr-sm">
              [{{ log.category }}]
            </span>
            <span>{{ log.message }}</span>
            <div v-if="log.data" class="q-ml-lg q-mt-xs">
              <pre class="text-caption">{{ JSON.stringify(log.data, null, 2) }}</pre>
            </div>
          </div>
        </q-scroll-area>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { multiOrderDebugger } from '~/utils/multiOrderDebugger'

const state = ref({
  accountCount: 0,
  mainQrcodeGenerated: false,
  multiQrcodeGenerated: 0,
  isolationActive: false,
})

const logs = ref<any[]>([])
const validationResult = ref<any>(null)
const isMonitoring = ref(false)
let monitoringController: any = null

function refreshState() {
  const newState = multiOrderDebugger.checkState()
  state.value = newState
  logs.value = multiOrderDebugger.getLogs()
  
  // 自动验证
  validationResult.value = multiOrderDebugger.validate()
}

function startMonitoring() {
  if (!isMonitoring.value) {
    monitoringController = multiOrderDebugger.startMonitoring()
    isMonitoring.value = true
  }
}

function stopMonitoring() {
  if (isMonitoring.value && monitoringController) {
    monitoringController.stop()
    monitoringController = null
    isMonitoring.value = false
    refreshState()
  }
}

function exportLogs() {
  multiOrderDebugger.exportLogs()
}

function clearLogs() {
  multiOrderDebugger.clearLogs()
  logs.value = []
}

function getLogColor(level: string) {
  switch (level) {
    case 'error': return 'red'
    case 'warn': return 'orange'
    case 'info': return 'blue'
    default: return 'grey'
  }
}

function formatTime(timestamp: string) {
  return new Date(timestamp).toLocaleTimeString()
}

onMounted(() => {
  refreshState()
})

onUnmounted(() => {
  stopMonitoring()
})
</script>

<style scoped>
.debug-panel {
  max-width: 800px;
  margin: 20px auto;
}

.status-overview {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.log-entry {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  border-left: 3px solid #e0e0e0;
  padding-left: 8px;
}

.validation-result {
  border-radius: 4px;
}

pre {
  background-color: #f5f5f5;
  padding: 4px;
  border-radius: 2px;
  overflow-x: auto;
  max-width: 100%;
}
</style>
