<script setup lang="ts">
import { computed, ref } from 'vue'
import { accountTokenList, currQrcode, isRoleMultiOpen, zfbUrls } from '~/compositions/useMultiOpen'
import { errNotify, successNotify } from '~/compositions/useNotify'

defineProps<{ qrcodeUrl: string }>()

// 二维码状态
const qrcodeStates = ref<Array<{ loaded: boolean, error: boolean }>>([])

// 计算所有二维码数据
const allQrcodes = computed(() => {
  const codes = []

  // 主账号二维码
  if (currQrcode.value) {
    codes.push({
      src: currQrcode.value,
      label: '主账号',
      account: '主账号',
      index: -1,
    })
  }

  // 多开账号二维码
  zfbUrls.value.forEach((url, index) => {
    if (url) {
      const account = accountTokenList.value[index]
      codes.push({
        src: url,
        label: `账号 ${index + 1}`,
        account: account?.account || `账号 ${index + 1}`,
        index,
      })
    }
  })

  return codes
})

// 复制二维码链接
async function copyQrcode(src: string, account: string) {
  try {
    // 从data URL中提取二维码内容（这里需要解析二维码，实际项目中可能需要专门的库）
    // 简化处理：直接复制图片数据
    await navigator.clipboard.writeText(src)
    successNotify(`已复制 ${account} 的二维码`)
  }
  catch (error) {
    errNotify('复制失败，请手动保存二维码')
  }
}

// 下载二维码
function downloadQrcode(src: string, account: string) {
  try {
    const link = document.createElement('a')
    link.href = src
    link.download = `qrcode_${account}_${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    successNotify(`已下载 ${account} 的二维码`)
  }
  catch (error) {
    errNotify('下载失败')
  }
}

// 二维码加载完成
function onQrcodeLoad(index: number) {
  if (!qrcodeStates.value[index])
    qrcodeStates.value[index] = { loaded: false, error: false }

  qrcodeStates.value[index].loaded = true
  qrcodeStates.value[index].error = false
}

// 二维码加载错误
function onQrcodeError(index: number) {
  if (!qrcodeStates.value[index])
    qrcodeStates.value[index] = { loaded: false, error: false }

  qrcodeStates.value[index].loaded = false
  qrcodeStates.value[index].error = true
}
</script>

<template>
  <div class="multi-qrcode-container">
    <!-- 二维码网格 -->
    <div class="multi-qrcode">
      <div
        v-for="(qrcode, index) in allQrcodes"
        :key="`qrcode-${qrcode.index}`"
        class="qrcode-item"
      >
        <div class="qrcode-wrapper">
          <!-- 二维码图片 -->
          <div class="qrcode-image">
            <q-img
              :src="qrcode.src"
              :alt="`${qrcode.account} 支付二维码`"
              class="qrcode-img"
              @load="onQrcodeLoad(index)"
              @error="onQrcodeError(index)"
            >
              <!-- 加载状态 -->
              <template #loading>
                <div class="qrcode-loading">
                  <q-spinner color="primary" size="md" />
                  <div class="text-caption q-mt-sm">
                    生成中...
                  </div>
                </div>
              </template>

              <!-- 错误状态 -->
              <template #error>
                <div class="qrcode-error">
                  <q-icon name="i-pixelarticons-close" color="red" size="md" />
                  <div class="text-caption q-mt-sm text-red">
                    加载失败
                  </div>
                </div>
              </template>
            </q-img>

            <!-- 二维码操作按钮 -->
            <div class="qrcode-actions">
              <q-btn
                round
                dense
                size="sm"
                color="primary"
                icon="i-pixelarticons-copy"
                class="action-btn"
                @click="copyQrcode(qrcode.src, qrcode.account)"
              >
                <q-tooltip>复制二维码</q-tooltip>
              </q-btn>
              <q-btn
                round
                dense
                size="sm"
                color="secondary"
                icon="i-pixelarticons-download"
                class="action-btn"
                @click="downloadQrcode(qrcode.src, qrcode.account)"
              >
                <q-tooltip>下载二维码</q-tooltip>
              </q-btn>
            </div>
          </div>

          <!-- 账号标签 -->
          <div class="qrcode-label">
            <q-chip
              :color="qrcode.index === -1 ? 'primary' : 'secondary'"
              text-color="white"
              size="sm"
              class="full-width"
            >
              {{ qrcode.label }}
            </q-chip>
            <div class="text-caption text-grey-7 q-mt-xs">
              {{ qrcode.account }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="qrcode-tips">
      <div v-if="isRoleMultiOpen" class="tip-item">
        <q-icon name="i-pixelarticons-info" color="orange" size="sm" />
        <span class="q-ml-sm text-caption">
          多开抢号角色分离费已根据是否是新账号计算，以实际支付为准
        </span>
      </div>

      <div class="tip-item">
        <q-icon name="i-pixelarticons-zap" color="green" size="sm" />
        <span class="q-ml-sm text-caption">
          请使用支付宝扫描对应的二维码完成支付
        </span>
      </div>

      <div class="tip-item">
        <q-icon name="i-pixelarticons-clock" color="blue" size="sm" />
        <span class="q-ml-sm text-caption">
          二维码有效期为15分钟，请及时完成支付
        </span>
      </div>
    </div>

    <!-- 支付进度 -->
    <div class="payment-progress">
      <div class="progress-header">
        <span class="text-subtitle2">支付进度</span>
        <span class="text-caption text-grey-7">
          {{ allQrcodes.length }} 个订单待支付
        </span>
      </div>
      <q-linear-progress
        :value="0"
        color="green"
        track-color="grey-3"
        class="q-mt-sm"
      />
    </div>
  </div>
</template>

<style scoped>
.multi-qrcode-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.multi-qrcode {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  justify-content: center;
  margin-bottom: 20px;
}

.qrcode-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-wrapper {
  width: 100%;
  max-width: 220px;
}

.qrcode-image {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
}

.qrcode-image:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  border-color: #1976d2;
}

.qrcode-img {
  width: 100%;
  height: 100%;
}

.qrcode-loading,
.qrcode-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
}

.qrcode-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.qrcode-image:hover .qrcode-actions {
  opacity: 1;
}

.action-btn {
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.9);
}

.qrcode-label {
  margin-top: 12px;
  text-align: center;
  width: 100%;
}

.qrcode-tips {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #2196f3;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.payment-progress {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .multi-qrcode {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .qrcode-image {
    width: 180px;
    height: 180px;
  }

  .qrcode-wrapper {
    max-width: 200px;
  }
}

/* 兼容旧版本的单个二维码样式 */
.qrcode:before {
  position: absolute;
  content: '支付宝二维码区域';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
