<script setup lang="ts">
import { useAction, useRob } from '~/compositions/useRob'
import type { FollowItem } from '~/type'
import { openZfbDialog } from '~/compositions/useZfb'
import RobMoreAction from '~/components/RobMoreAction.vue'
import IconBtn from '~/components/IconBtn.vue'

defineProps<{ row: FollowItem }>()
const { cancelFollow } = useAction()
const { state, stateColor } = useRob()
</script>

<template>
  <div class="col-xs-12 col-sm-6 q-pa-sm col-md-4">
    <q-card>
      <q-card-section flex="~ items-center">
        <q-img :src="row.goods_icon_url" height="32px" style="max-width: 62px" fit="contain" />
        <span> {{ row.goods_name }}</span>
        <q-space />
        <span font-bold>{{ row.goods_type_name }}{{ row.role_sect }}</span>
      </q-card-section>
      <q-card-section flex="~ justify-between" style="padding:0 16px">
        <q-chip color="red" text-color="white" square outline>
          ￥ {{ row.goods_price / 100 }}
        </q-chip>
        <q-chip color="teal" text-color="white" square outline>
          {{ row.followed_num || 0 }}人关注
        </q-chip>
        <q-chip :color="stateColor(row.state)" text-color="white" square outline>
          {{ state(row.state) }}
        </q-chip>
      </q-card-section>
      <q-card-section style="padding:16px 16px 0" text-right :class=" (row.remaining_time < 600 && row.remaining_time > 0) ? 'text-red' : ''">
        结束时间：{{ row.remaining_time === 0 ? '已失效' : new Date(row?.end_time || (new Date().getTime() + row.remaining_time * 1e3)).toLocaleString() }}
      </q-card-section>
      <q-card-actions align="right">
        <RobMoreAction :row="row" />
        <IconBtn
          color="red" icon="i-pixelarticons-mood-sad" tooltip="取消关注" dense flat
          @click="cancelFollow(row.consignment_id, row.type)"
        />
        <q-btn label="开始抢购" color="teal" @click="openZfbDialog(row)" />
      </q-card-actions>
    </q-card>
  </div>
</template>

<style scoped>

</style>
