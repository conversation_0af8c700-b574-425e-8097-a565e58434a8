<script setup lang="ts">
import IconBtn from '~/components/IconBtn.vue'
import type { FollowItem } from '~/type'
import { ajx3Detail } from '~/compositions/useAijx3'
import { jx3shDetail } from '~/compositions/useJx3Sh'
import { addFollowGoodsRowByNameAndPrice } from '~/compositions/useGoodsAutoFollow'

defineProps<{ row: FollowItem }>()
</script>

<template>
  <IconBtn tooltip="更多" dense flat icon="i-pixelarticons-menu">
    <q-menu>
      <q-list style="min-width: 100px">
        <q-item v-close-popup clickable @click="ajx3Detail(row.type, row.consignment_id)">
          <q-item-section>爱剑三详情</q-item-section>
        </q-item>
        <q-separator />
        <q-item v-if="row.type === 2" v-close-popup clickable @click="jx3shDetail(row.consignment_id)">
          <q-item-section>剑三商会详情</q-item-section>
        </q-item>
        <q-item v-if="row.type === 3" v-close-popup clickable @click="addFollowGoodsRowByNameAndPrice(row.goods_name, row.goods_price / 100)">
          <q-item-section>添加到自动关注列表</q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </IconBtn>
</template>

<style scoped>

</style>
