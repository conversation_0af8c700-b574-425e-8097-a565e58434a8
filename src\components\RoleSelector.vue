<script setup lang="ts">
import { ref, watch } from 'vue'
import { getUserRolesApi } from '~/api/wbl'
import type { UserRoleInfo } from '~/type'
import { errNotify } from '~/compositions/useNotify'
import { getLocalStorage } from '~/utils/utils'

interface Props {
  serverId: string
  disabled?: boolean
  placeholder?: string
}

interface Emits {
  (e: 'update:modelValue', value: UserRoleInfo | null): void
  (e: 'roleSelected', role: UserRoleInfo): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  placeholder: '请选择角色',
})

const emit = defineEmits<Emits>()

const selectedRole = defineModel<UserRoleInfo | null>()
const roles = ref<UserRoleInfo[]>([])
const loading = ref(false)

// 监听服务器ID变化，重新获取角色列表
watch(() => props.serverId, async (newServerId) => {
  if (newServerId) {
    await loadRoles(newServerId)
  }
  else {
    roles.value = []
    selectedRole.value = null
  }
}, { immediate: true })

// 获取角色列表
async function loadRoles(serverId: string) {
  if (!serverId)
    return

  loading.value = true
  try {
    const token = getLocalStorage('token') || ''
    const res = await getUserRolesApi(serverId, token)

    if (res.code === 1 && res.data?.list) {
      roles.value = res.data.list.filter(role => role.freeze === 0) // 过滤掉冻结的角色
    }
    else {
      errNotify(`获取角色列表失败：${res.msg || '未知错误'}`)
      roles.value = []
    }
  }
  catch (error) {
    console.error('获取角色列表失败:', error)
    errNotify('获取角色列表失败，请稍后重试')
    roles.value = []
  }
  finally {
    loading.value = false
  }
}

// 角色选择变化
function onRoleChange(role: UserRoleInfo | null) {
  selectedRole.value = role
  emit('update:modelValue', role)
  if (role)
    emit('roleSelected', role)
}

// 格式化角色显示文本
function formatRoleLabel(role: UserRoleInfo): string {
  return `${role.name} (Lv.${role.level})`
}
</script>

<template>
  <q-select
    :model-value="selectedRole"
    :options="roles"
    :loading="loading"
    :disable="disabled || !serverId"
    :placeholder="placeholder"
    clearable
    color="teal"
    @update:model-value="onRoleChange"
  >
    <template #option="scope">
      <q-item v-bind="scope.itemProps">
        <q-item-section>
          <q-item-label>{{ formatRoleLabel(scope.opt) }}</q-item-label>
          <q-item-label caption>
            角色ID: {{ scope.opt.id }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>

    <template #selected>
      <span v-if="selectedRole">
        {{ formatRoleLabel(selectedRole) }}
      </span>
    </template>

    <template #no-option>
      <q-item>
        <q-item-section class="text-grey">
          {{ serverId ? '该区服没有角色' : '请先选择服务器' }}
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<style scoped>
</style>
