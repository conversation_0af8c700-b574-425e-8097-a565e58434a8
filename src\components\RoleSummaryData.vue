<script setup lang="ts">
import type { RoleSummaryData } from '~/type'
import RoleSummaryItem from '~/components/RoleSummaryItem.vue'

defineProps<{ isShowRoleSummary: boolean, roleSummaryData: RoleSummaryData | undefined, position: { [key: string]: string } }>()
function getArenaLevel(base: any) {
  // 如果arena2V2Level存在，返回 x段（2V2）
  // 如果arena3V3Level存在，返回 x段（3V3）
  // 如果arena5V5Level存在，返回 x段（5V5）
  // 如果都不存在，返回空字符串
  if (base?.arena2V2Level)
    return `${base.arena2V2Level} 段（2V2）`
  else if (base.arena3V3Level)
    return `${base.arena3V3Level} 段（3V3）`
  else if (base.arena5V5Level)
    return `${base.arena5V5Level} 段（5V5）`
  else
    return '0 段（2V2）'
}
</script>

<template>
  <q-card v-if="roleSummaryData" flat :style="position" :class="isShowRoleSummary ? 'flex' : 'hidden'" class="role-summary-container">
    <q-list dense>
      <q-item-label header>
        基础信息
      </q-item-label>
      <RoleSummaryItem label="侠行点" :value="roleSummaryData.base.justice" />
      <RoleSummaryItem label="威名点" :value="roleSummaryData.base.prestige" />
      <RoleSummaryItem label="战阶等级" :value="`${roleSummaryData.base.militaryRank} 阶`" />
      <RoleSummaryItem label="名剑大会最高段" :value="getArenaLevel(roleSummaryData.base)" />
      <RoleSummaryItem label="获得“钦佩”声望数" :value="roleSummaryData.base.admireReputeCount" />
    </q-list>
    <q-list dense>
      <q-item-label header>
        外观信息
      </q-item-label>
      <RoleSummaryItem label="发型" :value="roleSummaryData.appearance.hair" />
      <RoleSummaryItem label="成衣" :value="roleSummaryData.appearance.shopExterior" />
      <RoleSummaryItem label="披风" :value="roleSummaryData.appearance.backCloak" />
      <RoleSummaryItem label="坐骑" :value="roleSummaryData.appearance.horse" />
      <RoleSummaryItem label="背部挂件" :value="roleSummaryData.appearance.back" />
      <RoleSummaryItem label="腰部挂件" :value="roleSummaryData.appearance.waist" />
    </q-list>
    <q-list dense>
      <q-item-label header>
        标签
      </q-item-label>
      <q-item v-for="item in roleSummaryData.tags" :key="item">
        <q-chip color="red" outline>
          {{ item }}
        </q-chip>
      </q-item>
    </q-list>
  </q-card>
</template>

<style scoped>
.role-summary-container {
  gap: 20px;
  align-items: flex-start;
  z-index: 1;
  display: flex;
  justify-content: center;
  position: fixed;
  width: 550px;
  padding: 20px 10px;
  border-radius: 8px;
  border: 1px solid #69c3ca;
}
</style>
