<script setup lang="ts">
import { ref } from 'vue'
import { useConcern } from '~/compositions/useConcern'
import GoodsNamePanel from '~/components/panels/GoodsNamePanel.vue'
import NoGoodsNamePanel from '~/components/panels/NoGoodsNamePanel.vue'
import PointFollowPanel from '~/components/panels/PointFollowPanel.vue'

const { isShowConcernDialog, onConcernDialogClose } = useConcern()
const tab = ref('goods-name')
</script>

<template>
  <q-dialog v-model="isShowConcernDialog" @hide="onConcernDialogClose">
    <q-card :min-w="`xs:100% ${tab === 'goods-name' ? 'sm:600px md:800px' : 'sm:500px md:500px'}`">
      <q-tabs
        v-model="tab"
        inline-label
        dense
        align="justify"
      >
        <q-tab :class="tab === 'goods-name' ? 'text-teal' : ''" name="goods-name" icon="i-pixelarticons-gift" label="指定外观" />
        <q-tab :class="tab === 'no-goods-name' ? 'text-teal' : ''" name="no-goods-name" icon="i-pixelarticons-shopping-bag" label="不指定外观" />
        <q-tab :class="tab === 'point' ? 'text-teal' : ''" name="point" icon="i-pixelarticons-shopping-bag" label="按积分比例" />
      </q-tabs>

      <q-separator />
      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="goods-name">
          <GoodsNamePanel />
        </q-tab-panel>
        <q-tab-panel name="no-goods-name">
          <NoGoodsNamePanel />
        </q-tab-panel>
        <q-tab-panel name="point">
          <PointFollowPanel />
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
