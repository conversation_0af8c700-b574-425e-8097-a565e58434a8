<script setup lang="ts">
import { useCurrRole } from '~/compositions/useCurrRole'

const { isShowCurrRole, categoryInfoWithCountMap, trinketCategoryItems, appearanceBoxScore, isReady } = useCurrRole()
</script>

<template>
  <q-dialog v-model="isShowCurrRole">
    <q-card min-h-300px min-w="md:900px sm:580px lg:1200px xl:1800px">
      <q-inner-loading
        :showing="!isReady"
        label="Please wait..."
        label-class="text-teal"
        label-style="font-size: 1.5em"
        z-1
      />
      <q-card-section flex="~ justify-center">
        <q-chip color="teal" text-color="white" size="xl">
          外观收藏分： {{ appearanceBoxScore }}
        </q-chip>
      </q-card-section>
      <q-separator />
      <q-card-section>
        <div v-for="(value) in categoryInfoWithCountMap" :key="value.category" flex>
          <q-chip color="teal" text-color="white">
            {{ `${value.category}：${value.count}` }}
          </q-chip>
          <div flex="~ warp">
            <q-chip v-for="(item, index) in value.subCategoryMap" :key="index">
              {{ `${item.category}：${item.count}` }}
            </q-chip>
          </div>
        </div>
      </q-card-section>
      <q-separator />
      <template v-for="(value) in trinketCategoryItems" :key="value.category">
        <q-card-section>
          <div flex="~ items-center">
            <q-chip color="teal" text-color="white">
              {{ value.category }}
            </q-chip>
            <div flex="~ wrap">
              <q-chip v-for="(item, index) in value.trinketSubCategoryItems" :key="index">
                {{ `${item.name}：${item.count}/${item.total}` }}
              </q-chip>
            </div>
          </div>
        </q-card-section>
        <q-separator />
      </template>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
