<script setup lang="ts">
import { useGoodsDetail } from '~/compositions/useRoleDetail'
import CardHeader from '~/components/CardHeader.vue'

const { isShowGoodsDetailDialog, currType, currId } = useGoodsDetail()
</script>

<template>
  <q-dialog v-model="isShowGoodsDetailDialog">
    <q-card min-h-822px min-w-980px>
      <CardHeader @close="isShowGoodsDetailDialog = false" />
      <q-card-section>
        <iframe :src="`https://jx3.seasunwbl.com/${currType}?consignment_id=${currId}`" width="940px" height="730px" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
