<script setup lang="ts">
import { useGoodsOrder } from '~/compositions/useGoodsOrder'
import { openGoodsDetailDialog } from '~/compositions/useRoleDetail'
import IconBtn from '~/components/IconBtn.vue'
import NoDataTd from '~/components/NoDataTd.vue'
import ItemRow from '~/components/ItemRow.vue'

const { firstPage, cancelOrder, isShow, toggleOrderType, getOrders, loading, columns, orderList, isEnd, currPage, currOrderType, prevPage, nextPage } = useGoodsOrder()

const states: { [key: number]: string } = { 1: '待付款', 3: '已支付', 2: '已关闭', 5: '已发货', 7: '已退款' }
const stateColor: { [key: number]: string } = { 1: 'red', 3: 'teal', 2: 'grey', 5: 'green', 7: 'orange' }
</script>

<template>
  <q-dialog v-model="isShow" persistent transition-show="scale" transition-hide="scale">
    <q-table
      class="bg-white"
      min-w="lg:!1200px md:!800px xl:!1300px sm:!580px"
      :grid="$q.screen.sm || $q.screen.xs || $q.screen.md"
      :rows="orderList"
      :columns="columns"
      row-key="order_id"
      :loading="loading"
      :pagination="{
        rowsPerPage: 10,
      }"
      :rows-per-page-options="[10]"
    >
      <template #top>
        <q-btn-toggle
          v-model="currOrderType"
          class="mr-4"
          border="teal 1px solid"
          toggle-color="teal"
          color="white"
          text-color="teal"
          no-caps
          unelevated rounded :options="[
            { label: '角色', value: 2 },
            { label: '外观', value: 3 },
          ]"
          @update:model-value="toggleOrderType"
        />
        <q-space />
        <IconBtn :loading="loading" flat color="teal" icon="i-pixelarticons-repeat" tooltip="刷新" @click="getOrders" />
        <IconBtn
          v-close-popup flat color="red" icon="i-pixelarticons-close"
          tooltip="关闭"
        />
      </template>
      <template #no-data>
        <NoDataTd msg="订单列表空空如也，大侠先去下单吧！" />
      </template>
      <template #body-cell-goods_list="props">
        <q-td>
          <q-img :src="props.row.goods_list[0].thumb" height="32px" style="max-width: 62px" fit="contain" />
          <span> {{ props.row.goods_list[0].info }}</span>
        </q-td>
      </template>
      <template #body-cell-state="{ row }">
        <q-td>
          <q-chip text-color="white" :color="stateColor[row.state]">
            {{ states[row.state] }}
          </q-chip>
        </q-td>
      </template>
      <template #body-cell-action="{ row }">
        <q-td>
          <IconBtn color="teal" icon="i-pixelarticons-contact" tooltip="查看详情" dense flat @click="openGoodsDetailDialog(row.goods_list[0].consignment_id, currOrderType)" />
          <IconBtn v-if="row.state === 1" color="red" icon="i-pixelarticons-notes-delete" tooltip="取消订单" dense flat @click="cancelOrder(row.order_id)" />
        </q-td>
      </template>
      <template #pagination>
        <q-btn
          v-if="currPage > 2"
          icon="first_page"
          color="grey-8"
          dense flat round
          @click="firstPage"
        />
        <q-btn
          icon="chevron_left"
          color="grey-8"
          dense flat round
          :disable="currPage === 1"
          @click="prevPage"
        />
        <q-btn
          icon="chevron_right"
          color="grey-8"
          round
          dense
          flat
          :disable="isEnd"
          @click="nextPage"
        />
      </template>
      <template #item="props">
        <div class="q-pa-sm col-xs-12 col-sm-6 col-md-6">
          <q-card>
            <q-list dense>
              <template v-for="col in props.cols" :key="col.order_id">
                <ItemRow v-if="col.label === '商品信息'" :label="col.label" :value="col.value[0].info" />
                <ItemRow v-else-if="col.label === '订单状态'" :label="col.label" :value="states[col.value]" />
                <ItemRow v-else-if="col.label !== '操作'" :label="col.label" :value="col.value" />
                <q-item v-else-if="col.label === '操作' && props.row.state === 1">
                  <q-btn class="full-width mb-10px" label="取消订单" color="red" icon="i-pixelarticons-notes-delete" @click="cancelOrder(props.row.order_id)" />
                </q-item>
              </template>
            </q-list>
          </q-card>
        </div>
      </template>
    </q-table>
  </q-dialog>
</template>

<style scoped>

</style>
