<script setup lang="ts">
import CardHeader from '~/components/CardHeader.vue'
import { loginWbl, useLoginDialog } from '~/compositions/useMultiOpen'

const { account, pwd } = useLoginDialog()
const isShowLoginDialog = defineModel<boolean>()
</script>

<template>
  <q-dialog v-model="isShowLoginDialog" persistent>
    <q-card min-w-350px>
      <CardHeader title="登录" @close="isShowLoginDialog = false" />
      <q-card-section>
        <q-input v-model="account" color="teal" :rules="[val => !!val || '请输入账号']" label="账号" />
        <q-input v-model="pwd" color="teal" :rules="[val => !!val || '请输入密码']" type="password" label="密码" />
      </q-card-section>
      <q-card-actions align="right">
        <q-btn color="teal" label="登录" @click="loginWbl(account, pwd)" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
