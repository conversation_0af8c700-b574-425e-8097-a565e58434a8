<script setup lang="ts">
import CardHeader from '~/components/CardHeader.vue'
import { useMaintenance } from '~/compositions/useMaintenance'
import { useCaptcha } from '~/compositions/useCaptcha'
import { useLoginCaptcha } from '~/compositions/useLoginCaptcha'

const { loadCaptcha, captchaState, captchaText } = useCaptcha()
const { loadLoginCaptcha, loginCaptchaState, loginCaptchaText } = useLoginCaptcha()
const { onSave, account, password, itemId, isShowMaintenanceDialog } = useMaintenance()
</script>

<template>
  <q-dialog v-model="isShowMaintenanceDialog" persistent>
    <q-card min-w-350px>
      <CardHeader title="蹲开服" @close="isShowMaintenanceDialog = false" />
      <q-card-section>
        <div text-red>
          仅支持蹲角色，且不支持提前选择转服转阵营服务。
        </div>
        <div>
          验证码会失效，建议每隔15分钟重新获取一下验证码。
        </div>
        <div>
          开服后如果商品处于在售期，则直接出码，如果处于公示期则进入倒计时等待出码（验证码失效出不了码时会跳万宝楼主页）。
        </div>
      </q-card-section>
      <q-card-section>
        <q-form
          class="q-gutter-md"
          @submit="onSave"
        >
          <q-input v-model="account" dense color="teal" :rules="[val => !!val || '请输入账号']" label="账号" />
          <q-input v-model="password" dense color="teal" :rules="[val => !!val || '请输入密码']" label="密码" />
          <q-input v-model="itemId" dense color="teal" :rules="[val => !!val || `请输入角色的商品编号`]" label="商品编号" />
          <div>
            <span>订单验证码：</span>
            <q-chip :color="captchaState ? 'green' : 'red'" text-color="white">
              {{ captchaText }}
            </q-chip>
          </div>
          <div>
            <span>登录验证码：</span>
            <q-chip :color="loginCaptchaState ? 'green' : 'red'" text-color="white">
              {{ loginCaptchaText }}
            </q-chip>
          </div>
          <div flex="~ justify-around">
            <q-btn color="teal" label="获取订单验证码" @click="loadCaptcha" />
            <q-btn color="teal" label="获取登录验证码" @click="loadLoginCaptcha" />
          </div>
          <div flex="~ justify-end">
            <q-btn color="teal" type="submit" label="保存" />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
