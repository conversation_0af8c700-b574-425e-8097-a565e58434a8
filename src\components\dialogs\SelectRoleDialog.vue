<script setup lang="ts">
import { isShowSelectRoleDialog, useSelectRole } from '~/compositions/useSelectRole'
import CardHeader from '~/components/CardHeader.vue'

const { submit, roleList, selectRole, selectServer, serverList, selectServerHandler } = useSelectRole()
</script>

<template>
  <q-dialog v-model="isShowSelectRoleDialog">
    <q-card min-w-350px>
      <CardHeader title="选择角色" @close="isShowSelectRoleDialog = false" />
      <q-card-section>
        <q-select
          v-model="selectServer"
          color="teal"
          :options="serverList"
          :option-label="(opt) => opt.name"
          :option-value="(opt) => opt.id"
          label="请选择区服"
          @update:model-value="selectServerHandler"
        />
        <q-select
          v-model="selectRole"
          color="teal"
          :options="roleList"
          :option-label="(opt) => opt.name"
          :option-value="(opt) => opt.id"
          label="请选择角色"
        />
      </q-card-section>
      <q-card-actions align="right">
        <q-btn label="确定" color="teal" @click="submit" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
