<script setup lang="ts">
import CardHeader from '~/components/CardHeader.vue'
import {
  currPayWay,
  isAutoHideMenu,
  isShowSettingDialog,
  onAutoHideMenuChange,
  onPayWayChange,
  openServiceRepeatDelayTime,
  orderRetryTimes,
  payRetryTimes,
  payWays,
  preRobTime,
} from '~/compositions/useSetting'
import { setLocalStorage } from '~/utils/utils'

function onChanges() {
  preRobTime.value = +preRobTime.value
  if (preRobTime.value >= 0)
    setLocalStorage('preRobTime', +preRobTime.value)
  else preRobTime.value = 0
}
function onChangePayRetryTimes() {
  payRetryTimes.value = +payRetryTimes.value
  if (payRetryTimes.value >= 0)
    setLocalStorage('payRetryTimes', payRetryTimes.value)
  else
    payRetryTimes.value = 0
}

function onChangeOrderRetryTimes() {
  orderRetryTimes.value = +orderRetryTimes.value
  if (orderRetryTimes.value >= 0)
    setLocalStorage('orderRetryTimes', orderRetryTimes.value)
  else
    orderRetryTimes.value = 0
}

function onChangeOpenServiceRepeatDelayTime() {
  openServiceRepeatDelayTime.value = +openServiceRepeatDelayTime.value
  if (openServiceRepeatDelayTime.value >= 0)
    setLocalStorage('openServiceRepeatDelayTime', openServiceRepeatDelayTime.value)
  else
    openServiceRepeatDelayTime.value = 5000
}
</script>

<template>
  <q-dialog v-model="isShowSettingDialog">
    <q-card min-w="xs:300px md:500px sm:500px lg:500px xl:500px">
      <CardHeader title="设置参数" @close="isShowSettingDialog = false" />
      <q-card-section>
        <q-input v-model="preRobTime" :rules="[(val:number) => val >= 0 || '该参数必须大于大于 0']" type="number" color="teal" label="公示期结束前的刷新频率（单位：ms）" hint="请谨慎修改该参数，并非刷新频率越低越快，当刷新频率过低时，可能导致网络异常，生成支付二维码失败。" @update:model-value="onChanges" />
      </q-card-section>
      <q-card-section>
        <q-input v-model="payRetryTimes" :rules="[(val:number) => val >= 0 || '该参数必须大于等于 0']" type="number" color="teal" label="支付失败重试次数" hint="当提示“请勿重新提交支付请求”时，可尝试修改该值。" @update:model-value="onChangePayRetryTimes" />
      </q-card-section>
      <q-card-section>
        <q-input v-model="orderRetryTimes" :rules="[(val:number) => val >= 0 || '该参数必须大于等于 0']" type="number" color="teal" label="创建订单失败重试次数" hint="当提示“订单创建失败”时，可尝试修改该值。" @update:model-value="onChangeOrderRetryTimes" />
      </q-card-section>
      <q-card-section>
        <q-input v-model="openServiceRepeatDelayTime" :rules="[(val:number) => val >= 0 || '该参数必须大于等于 0']" type="number" color="teal" label="万宝楼维护时自动刷新频率（单位：ms）" hint="该值仅在万宝楼维护时生效，例：当该值为 5000 时，脚本会每 5 秒检查一下是否开楼。" @update:model-value="onChangeOpenServiceRepeatDelayTime" />
      </q-card-section>
      <q-card-section v-if="!$q.screen.xs">
        <q-checkbox v-model="isAutoHideMenu" color="teal" label="自动隐藏右下角菜单" @update:model-value="onAutoHideMenuChange" />
      </q-card-section>
      <q-card-section class="!pb-50px">
        <div>支付方式</div>
        <q-option-group
          v-model="currPayWay"
          :options="payWays"
          color="teal"
          inline
          @update:model-value="onPayWayChange"
        />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<style scoped>

</style>
