<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
import { columns, goodsTypes, robTableLoading, rows, selectedGoodsType, useAction, useRob } from '~/compositions/useRob'
import RobTableTop from '~/components/dialogs/rob/robTableTop.vue'
import IconBtn from '~/components/IconBtn.vue'
import { openZfbDialog } from '~/compositions/useZfb'
import { successNotify } from '~/compositions/useNotify'
import HoverImgTd from '~/components/HoverImgTd.vue'
import RobGridRow from '~/components/RobGridRow.vue'
import { openGoodsDetailDialog } from '~/compositions/useRoleDetail'
import NoDataTd from '~/components/NoDataTd.vue'
import RobMoreAction from '~/components/RobMoreAction.vue'
import { clockNotification } from '~/compositions/useClock'
import SelectRoleDialog from '~/components/dialogs/SelectRoleDialog.vue'

const { cancelFollow } = useAction()
const { isShowRob, state, stateColor } = useRob()
const { copy, isSupported } = useClipboard()

function copyId(id: string) {
  if (isSupported.value) {
    copy(id)
    successNotify(`商品id [ ${id} ] 复制成功`)
  }
}

function filter(rows: readonly any[], terms: any) {
  return rows.filter(row => terms.includes(`${row.goods_type_name}${row.role_sect}`))
}
</script>

<template>
  <q-dialog v-model="isShowRob" persistent transition-show="scale" transition-hide="scale">
    <q-table
      class="my-sticky-last-column-table bg-white lg:!min-w-1400px md:!min-w-1000px xl:!min-w-1800px"
      :grid="$q.screen.sm || $q.screen.xs || $q.screen.md"
      :rows="rows"
      :columns="columns"
      row-key="consignment_id"
      :loading="robTableLoading"
      :pagination="{
        rowsPerPage: 10,
      }"
      :rows-per-page-options="[10]"
      :filter="selectedGoodsType"
      :filter-method="filter"
    >
      <template #top="props">
        <RobTableTop :props="props" />
      </template>
      <template #header="props">
        <q-tr :props="props">
          <q-th
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            <template v-if="col.label === '类型'">
              <span cursor-pointer>
                {{ col.label }}
                <q-menu>
                  <div flex="~ col" p-16px>
                    <div flex gap-10px pb-10px>
                      <q-btn color="teal" dense label="全选" @click="selectedGoodsType = goodsTypes" />
                      <q-btn color="teal" dense label="全不选" @click="selectedGoodsType = []" />
                    </div>
                    <q-checkbox v-for="item in goodsTypes" v-model="selectedGoodsType" dense :val="item" :label="item" color="teal" />
                  </div>
                </q-menu>
              </span>
            </template>
            <template v-else>
              {{ col.label }}
            </template>
          </q-th>
        </q-tr>
      </template>
      <template #no-data>
        <NoDataTd msg="关注列表空空如也，少侠先点点关注吧！" />
      </template>
      <template #body-cell-name="props">
        <HoverImgTd :row="props.row" />
      </template>
      <!--   公示状态 3：公示中  5：在售中  -->
      <template #body-cell-state="props">
        <q-td class="text-center">
          <q-chip :color="stateColor(props.row.state)" text-color="white" dense square>
            {{ state(props.row.state) }}
          </q-chip>
        </q-td>
      </template>
      <template #body-cell-goods_price="props">
        <q-td class="text-left text-red font-bold">
          <q-icon name="jx3_yuan" />
          {{ props.row.goods_price / 100 }}
          <template v-if="props.row.follow_goods_price !== props.row.goods_price">
            <div class="text-9px font-medium" :class="props.row.follow_goods_price > props.row.goods_price ? 'text-green' : 'text-red'">
              <span>关注后：</span>
              <q-icon :name="props.row.follow_goods_price > props.row.goods_price ? 'i-pixelarticons-arrow-down' : 'i-pixelarticons-arrow-up'" />
              <span>
                ￥{{ Math.abs(props.row.follow_goods_price - props.row.goods_price) / 100 }}
              </span>
            </div>
          </template>
        </q-td>
      </template>
      <template #body-cell-followed_num="props">
        <q-td class="text-left" :class="(props.row?.followed_num || 0) > 50 ? 'text-red' : ''">
          <q-icon text-red name="i-pixelarticons-heart" />
          {{ props.row.followed_num || 0 }}
        </q-td>
      </template>
      <template #body-cell-rewards="props">
        <q-td v-if="props.row.appearanceBaseInfo?.rewards" class="text-left">
          {{ props.row.appearanceBaseInfo.rewards }} <span text-gray-4 ml-1>(1:{{ Math.floor(Number(props.row.appearanceBaseInfo.rewards) / props.row.goods_price * 100) }})</span>
        </q-td>
        <q-td v-else class="text-left text-red">
          无
        </q-td>
      </template>
      <template #body-cell-consignment_id="props">
        <q-td cursor-pointer @click="copyId(props.row.consignment_id)">
          {{ props.row.consignment_id }}
        </q-td>
      </template>
      <template #body-cell-action="props">
        <q-td :props="props" class="flex">
          <IconBtn color="teal" icon="i-pixelarticons-zap" tooltip="抢" dense flat @click="openZfbDialog(props.row)" />
          <IconBtn color="teal" icon="i-pixelarticons-contact" tooltip="查看详情" dense flat @click="openGoodsDetailDialog(props.row.consignment_id, props.row.type)" />
          <IconBtn
            color="red" icon="i-pixelarticons-mood-sad" tooltip="取消关注" dense flat
            @click="cancelFollow(props.row.consignment_id, props.row.type)"
          />
          <RobMoreAction :row="props.row" />
          <IconBtn
            v-if="props.row.state === 3 && props.row.remaining_time > 2 * 60" :color="props.row.clock ? 'red' : 'orange'" :icon="props.row.clock ? 'i-pixelarticons-notification-off' : 'i-pixelarticons-notification'"
            :tooltip="props.row.clock ? '取消提醒' : '开售前两分钟提醒我'" dense flat @click="clockNotification(props.row)"
          />
        </q-td>
      </template>
      <template #item="{ row }">
        <RobGridRow :row="row" />
      </template>
    </q-table>
  </q-dialog>
  <SelectRoleDialog />
</template>

<style>
.my-sticky-last-column-table thead tr:last-child th:last-child {
  /* 设置固定列的表头颜色 */
  background-color: #fff;
}
.my-sticky-last-column-table td {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.my-sticky-last-column-table td:last-child {
  /* 设置被固定列的背景颜色 */
  background-color: #fff;
}

.my-sticky-last-column-table th:last-child,
.my-sticky-last-column-table td:last-child {
  width: 186px;
  /* 固定最后一列 */
  position: sticky;
  right: 0;
  z-index: 1;
}
</style>
