<script setup lang="ts">
import { ref } from 'vue'
import { getList, robTableLoading, useAction } from '~/compositions/useRob'
import { useCaptcha } from '~/compositions/useCaptcha'
import IconBtn from '~/components/IconBtn.vue'
import { useConcern } from '~/compositions/useConcern'
import { openGoodsOrder } from '~/compositions/useGoodsOrder'
import { accountTokenList, isOpenLoginDialog, isRoleMultiOpen, isSkinMultiOpen } from '~/compositions/useMultiOpen'
import MultiOpenLoginDialog from '~/components/dialogs/MultiOpenLoginDialog.vue'
import { currPayWay, isShowSettingDialog } from '~/compositions/useSetting'

defineProps(['props'])

const { openConcernDialog } = useConcern()
const { addFollow, inputFollowId, cancelAllInvalidFollow, cancelAllInvalidFollowLoading } = useAction()
const { loadCaptcha, captchaState, captchaLoading, captchaText } = useCaptcha()
const goodsType = ref<2 | 3>(2)
</script>

<template>
  <div v-if="$q.screen.xs" w-full flex="~ col">
    <div flex="~ justify-end">
      <IconBtn
        :loading="captchaLoading" flat color="teal" icon="i-pixelarticons-play" tooltip="开始验证"
        @click="loadCaptcha"
      />
      <IconBtn :loading="robTableLoading" flat color="teal" icon="i-pixelarticons-repeat" tooltip="刷新" @click="getList()" />
      <IconBtn flat tooltip="购买订单记录" icon="i-pixelarticons-script-text" color="teal" @click="openGoodsOrder" />
      <template v-if="currPayWay === 'qrcode'">
        <IconBtn flat color="teal" icon="i-pixelarticons-users" tooltip="多开模式" @click="isOpenLoginDialog = true" />
        <MultiOpenLoginDialog />
      </template>
      <IconBtn
        flat color="red" icon="i-pixelarticons-heart"
        tooltip="批量关注" @click="openConcernDialog"
      />
      <IconBtn
        flat color="red" :loading="cancelAllInvalidFollowLoading" icon="i-pixelarticons-mood-sad"
        tooltip="批量取消失效商品" @click="cancelAllInvalidFollow"
      />
      <IconBtn v-close-popup flat color="red" icon="i-pixelarticons-close" tooltip="关闭" />
    </div>
    <div flex="~ justify-end">
      <q-chip v-if="isRoleMultiOpen && currPayWay === 'qrcode'" color="green" text-color="white">
        {{ `多开抢号模式，当前有效账号数：${accountTokenList.length + 1}` }}
      </q-chip>
      <q-chip v-else-if="isSkinMultiOpen && currPayWay === 'qrcode'" color="green" text-color="white">
        {{ `多开抢外观模式，当前有效账号数：${accountTokenList.length + 1}` }}
      </q-chip>
      <q-chip v-else :color="captchaState ? 'green' : 'red'" text-color="white">
        {{ captchaText }}
      </q-chip>
    </div>

    <div flex="~ justify-end">
      <q-btn-toggle
        v-model="goodsType"
        dense
        class="mr-4"
        border="teal 1px solid"
        toggle-color="teal"
        color="white"
        text-color="teal"
        no-caps unelevated
        padding="0 6px"
        :options="[
          { label: '角色', value: 2 },
          { label: '外观', value: 3 },
        ]"
      />
      <q-input v-model="inputFollowId" color="teal" dense style="width: 140px" label="商品ID" />
      <IconBtn icon="i-pixelarticons-heart" flat color="red" tooltip="关注" @click="addFollow(goodsType)" />
    </div>
  </div>
  <template v-else>
    <div flex>
      <q-btn-toggle
        v-model="goodsType"
        class="mr-4"
        border="teal 1px solid"
        toggle-color="teal"
        color="white"
        text-color="teal"
        no-caps unelevated rounded
        :options="[
          { label: '角色', value: 2 },
          { label: '外观', value: 3 },
        ]"
      />
      <q-input v-model="inputFollowId" color="teal" dense label="商品ID" />
      <IconBtn icon="i-pixelarticons-heart" flat color="red" tooltip="关注" @click="addFollow(goodsType)" />
    </div>
    <q-space />
    <div>
      <q-chip v-if="isRoleMultiOpen && currPayWay === 'qrcode'" color="green" text-color="white">
        {{ `多开抢号模式，当前有效账号数：${accountTokenList.length + 1}` }}
      </q-chip>
      <q-chip v-else-if="isSkinMultiOpen && currPayWay === 'qrcode'" color="green" text-color="white">
        {{ `多开抢外观模式，当前有效账号数：${accountTokenList.length + 1}` }}
      </q-chip>
      <q-chip v-else :color="captchaState ? 'green' : 'red'" text-color="white">
        {{ captchaText }}
      </q-chip>
      <IconBtn
        :loading="captchaLoading" flat color="teal" icon="i-pixelarticons-play" tooltip="开始验证"
        @click="loadCaptcha"
      />
      <IconBtn :loading="robTableLoading" flat color="teal" icon="i-pixelarticons-repeat" tooltip="刷新" @click="getList()" />
      <IconBtn flat color="teal" icon="i-pixelarticons-power" tooltip="设置" @click="isShowSettingDialog = true" />
      <IconBtn
        flat :icon="props.inFullscreen ? 'i-pixelarticons-zoom-out' : 'i-pixelarticons-zoom-in'"
        color="teal" :tooltip="props.inFullscreen ? '退出全屏' : '全屏'" @click="props.toggleFullscreen"
      />
      <IconBtn flat tooltip="购买订单记录" icon="i-pixelarticons-script-text" color="teal" @click="openGoodsOrder" />
      <template v-if="currPayWay === 'qrcode'">
        <IconBtn flat color="teal" icon="i-pixelarticons-users" tooltip="多开模式" @click="isOpenLoginDialog = true" />
        <MultiOpenLoginDialog />
      </template>
      <IconBtn
        flat color="red" icon="i-pixelarticons-heart"
        tooltip="批量关注" @click="openConcernDialog"
      />
      <IconBtn
        flat color="red" :loading="cancelAllInvalidFollowLoading" icon="i-pixelarticons-mood-sad"
        tooltip="批量取消失效商品" @click="cancelAllInvalidFollow"
      />
      <IconBtn
        v-show="!props.inFullscreen" v-close-popup flat color="red" icon="i-pixelarticons-close"
        tooltip="关闭"
      />
    </div>
  </template>
</template>

<style scoped>

</style>
