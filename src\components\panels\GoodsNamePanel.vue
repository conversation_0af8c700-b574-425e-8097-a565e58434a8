<script setup lang="ts">
import IconBtn from '~/components/IconBtn.vue'
import useGoodsAutoFollow, { autoFollowAll, autoFollowSingle } from '~/compositions/useGoodsAutoFollow'
import NoDataTd from '~/components/NoDataTd.vue'
import { isStopFollow } from '~/compositions/useConcern'

const { rows, columns, maxPublicTime, minPublicTime, addGoodsRow, onNameChange, removeGoodsRow } = useGoodsAutoFollow()
</script>

<template>
  <q-card>
    <q-table
      class="my-sticky-last-column-table"
      title="Treats"
      :rows="rows"
      :columns="columns"
      row-key="name"
      binary-state-sort
      :pagination="{
        rowsPerPage: 10,
      }"
      :rows-per-page-options="[10]"
    >
      <template #no-data>
        <NoDataTd msg="请先添加商品" />
      </template>
      <template #top>
        <q-input
          v-model.number="minPublicTime"
          color="teal"
          class="w-220px"
          label="最小公示期剩余时间（单位：小时）"
          hide-hint
          type="number"
          hint="例：填写 1，则公示期剩余时间大于等于 1 小时且满足其他条件的外观将被关注。"
          :rules="[(val:number) => (val >= 0 && val < maxPublicTime) || '公示期剩余时间必须大于等于0并且小于最大公示期剩余时间']"
        />
        <q-icon class="i-pixelarticons-minus mx-10px text-gray" />
        <q-input
          v-model.number="maxPublicTime"
          color="teal"
          class="w-220px"
          label="最大公示期剩余时间（单位：小时）"
          hide-hint
          type="number"
          hint="例：填写 5，则公示期剩余时间小于等于 5 小时且满足其他条件的外观将被关注。"
          :rules="[(val:number) => (val > 0 && val > minPublicTime) || '公示期剩余时间必须大于0并且大于最小公示期剩余时间']"
        />
        <q-space />
        <q-btn color="teal" label="添加商品" @click="addGoodsRow" />
        <q-btn class="ml-10px" :disable="isStopFollow" color="teal" label="一键关注" @click="autoFollowAll()" />
      </template>
      <template #body="props">
        <q-tr :props="props">
          <q-td key="name" :props="props">
            {{ props.row.name?.split('\n').join('、') || '单击此处填写外观' }}
            <q-popup-edit v-slot="scope" v-model="props.row.name" auto-save @hide="onNameChange(props.row)">
              <q-input
                v-model="scope.value"
                color="teal"
                type="textarea"
                hint="输入完整外观名称，多个外观回车分隔，最多支持5个外观"
                dense
                autofocus
              />
            </q-popup-edit>
          </q-td>
          <q-td key="price" :props="props">
            {{ props.row.price }}
            <q-popup-edit v-slot="scope" v-model="props.row.price" auto-save>
              <q-input
                v-model="scope.value"
                color="teal" dense autofocus
                type="number"
                :rules="[(val:number) => val > 0 || '价格必须大于0']"
                hint="输入外观期望价格"
                @keyup.enter="scope.set"
              />
            </q-popup-edit>
          </q-td>
          <q-td key="action" :props="props">
            <IconBtn color="red" icon="i-pixelarticons-heart" :disable="isStopFollow" tooltip="一键关注" dense flat @click="autoFollowSingle(props.row.name, props.row.price)" />
            <IconBtn color="red" icon="i-pixelarticons-trash" tooltip="删除" dense flat @click="removeGoodsRow(props.rowIndex)" />
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </q-card>
</template>

<style scoped>
.my-sticky-last-column-table thead tr:last-child th:last-child {
  /* 设置固定列的表头颜色 */
  background-color: #fff;
}
.my-sticky-last-column-table td {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

.my-sticky-last-column-table td:last-child {
  /* 设置被固定列的背景颜色 */
  background-color: #fff;
}

.my-sticky-last-column-table th:last-child,
.my-sticky-last-column-table td:last-child {
  /* 固定最后一列 */
  position: sticky;
  right: 0;
  z-index: 1;
}
</style>
