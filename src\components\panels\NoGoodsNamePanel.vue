<script setup lang="ts">
import {
  autoFollowNoGoodsName,
  currPriceLabel,
  currPriceMode,
  priceModeOptions,
  useNoGoodsNameFollow,
} from '~/compositions/useNoGoodsNameFollow'
import { closeConcernDialog, isStopFollow } from '~/compositions/useConcern'

const { priceDiscount, minFollowNum, maxPublicTime, minPublicTime, noFollowGoodsType, noFollowGoods } = useNoGoodsNameFollow()
// 商品类型
const typeOptions = ['外观礼盒', '上衣', '发型', '披风', '帽子', '背挂', '腰挂', '面挂', '肩饰', '眼饰', '手饰', '佩囊', '小头像', '宠物', '挂宠', '坐骑', '马具', '其他']
</script>

<template>
  <q-card w="xs:100% sm:500px">
    <q-card-section>
      <div class="font-size-20px">
        请填写过滤条件
      </div>
    </q-card-section>
    <q-separator />
    <q-card-section flex="~ col" gap-10px>
      <span>外观折扣价格基准</span>
      <q-option-group
        v-model="currPriceMode"
        :options="priceModeOptions"
        color="teal"
        inline
      />
      <q-input
        v-model.number="priceDiscount"
        color="teal"
        hide-hint
        label="外观价格折扣（单位：折）"
        :hint="`例：某外观${currPriceLabel}为 100 元，填写 8，则关注价格为 80 元以下的外观`"
        type="number"
        :rules="[(val:number) => (val >= 0 && val <= 10) || '折扣范围为0-10']"
      />
      <q-input
        v-model.number="minPublicTime"
        color="teal"
        label="最小公示期剩余时间（单位：小时）"
        hide-hint
        type="number"
        hint="例：填写 1，则公示期剩余时间大于等于 1 小时且满足其他条件的外观将被关注。"
        :rules="[(val:number) => (val >= 0 && val < maxPublicTime) || '公示期剩余时间必须大于等于 0 并且小于最大公示期剩余时间']"
      />
      <q-input
        v-model.number="maxPublicTime"
        color="teal"
        label="最大公示期剩余时间（单位：小时）"
        hide-hint
        type="number"
        hint="例：填写 12，则公示期剩余时间小于等于 12 小时且满足其他条件的外观将被关注。"
        :rules="[(val:number) => (val > 0 && val > minPublicTime) || '公示期剩余时间必须大于 0 并且大于最小公示期剩余时间']"
      />
      <q-input
        v-model.number="minFollowNum"
        color="teal"
        label="外观最小关注数"
        hide-hint
        hint="例：填写 10，则关注数大于等于 10 且满足其他条件的外观将被关注。"
        type="number"
        :rules="[(val:number) => val > 0 || '关注数必须大于0']"
      />
      <q-select
        v-model="noFollowGoodsType"
        color="teal"
        :options="typeOptions"
        label="不关注的外观类型"
        multiple
      />
      <q-select
        v-model="noFollowGoods"
        color="teal"
        label="不关注的外观"
        hint="输入外观名后按回车添加，请确保输入的外观名正确"
        hide-hint multiple use-input use-chips hide-dropdown-icon
        input-debounce="0"
        new-value-mode="add-unique"
      />
    </q-card-section>
    <q-separator />
    <q-card-actions align="right">
      <q-btn color="teal" label="关闭" @click="closeConcernDialog" />
      <q-btn color="teal" :disable="isStopFollow" label="一键关注" @click="autoFollowNoGoodsName" />
    </q-card-actions>
  </q-card>
</template>

<style scoped>

</style>
