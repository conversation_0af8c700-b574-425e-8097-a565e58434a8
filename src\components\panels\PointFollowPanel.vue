<script setup lang="ts">
import { autoFollowByPoint, usePointFollow } from '~/compositions/usePointFollow'
import { closeConcernDialog, isStopFollow } from '~/compositions/useConcern'

const { followGoodsType, minPoint, publicTime, noFollowGoods } = usePointFollow()
// 商品类型
const typeOptions = [
  { label: '外观礼盒', value: 1 },
  { label: '上衣', value: 2 },
  { label: '发型', value: 3 },
  { label: '披风', value: 4 },
  { label: '帽子', value: 5 },
  { label: '背挂', value: 6 },
  { label: '腰挂', value: 7 },
  { label: '面挂', value: 8 },
  { label: '肩饰', value: 9 },
  { label: '眼饰', value: 10 },
  { label: '手饰', value: 11 },
  { label: '佩囊', value: 12 },
  { label: '小头像', value: 13 },
  { label: '宠物', value: 14 },
  { label: '挂宠', value: 15 },
  { label: '坐骑', value: 16 },
  { label: '马具', value: 17 },
  { label: '其他', value: 18 },
]
</script>

<template>
  <q-card w="xs:100% sm:500px">
    <q-card-section>
      <div class="font-size-20px">
        请填写过滤条件
      </div>
    </q-card-section>
    <q-separator />
    <q-card-section flex="~ col" gap-10px>
      <q-input
        v-model.number="minPoint"
        color="teal"
        label="积分比"
        hide-hint
        hint="例：填写 30，则 RMB:积分 为 1:30 时且满足其他条件的外观将被关注。"
        type="number"
        :rules="[(val:number) => val > 0 || '积分比必须大于0']"
      />
      <div>公示期时间范围</div>
      <q-range
        v-model="publicTime"
        class="mt-6"
        :min="0"
        :max="72"
        :step="0.5"
        label-always
        color="teal"
      />
      <q-select
        v-model="followGoodsType"
        color="teal"
        :options="typeOptions"
        label="关注的外观类型"
        max-values="5"
        multiple
        emit-value
        map-options
      />
      <q-select
        v-model="noFollowGoods"
        color="teal"
        label="不关注的外观"
        hint="输入外观名后按回车添加，请确保输入的外观名正确"
        hide-hint multiple use-input use-chips hide-dropdown-icon
        input-debounce="0"
        new-value-mode="add-unique"
      />
    </q-card-section>
    <q-separator />
    <q-card-actions align="right">
      <q-btn color="teal" label="关闭" @click="closeConcernDialog" />
      <q-btn color="teal" :disable="isStopFollow" label="一键关注" @click="autoFollowByPoint" />
    </q-card-actions>
  </q-card>
</template>

<style scoped>

</style>
