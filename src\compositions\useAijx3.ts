import { getAjx3AccountDetailsApi, getAjx3GoodsDetailsApi } from '~/api/ajx3'
import { getLocalStorage, getSessionStorage, openNewWin, setSessionStorage } from '~/utils/utils'
import { errNotify, successNotify } from '~/compositions/useNotify'
import { addFollowApiByToken } from '~/api/wbl'

async function getAccountId(consignment_id: string) {
  const accountIds = getSessionStorage('aijx3AccountIds', {})
  if (accountIds?.[consignment_id])
    return accountIds[consignment_id]
  const res = await getAjx3AccountDetailsApi(consignment_id) as any
  if (res?.zhanghaoDataList?.length) {
    accountIds[consignment_id] = res.zhanghaoDataList[0].zhanghaoId
    setSessionStorage('aijx3AccountIds', accountIds)
    return accountIds[consignment_id]
  }
  else {
    return ''
  }
}

async function getGoodsId(consignment_id: string) {
  const goodsIds = getSessionStorage('aijx3AccountIds', {})
  if (goodsIds?.[consignment_id])
    return goodsIds[consignment_id]
  const res = await getAjx3GoodsDetailsApi(consignment_id) as any
  if (res?.data?.records?.length) {
    goodsIds[consignment_id] = res.data.records[0].recordId
    setSessionStorage('aijx3AccountIds', goodsIds)
    return goodsIds[consignment_id]
  }
  else {
    return ''
  }
}

export async function ajx3Detail(goodsType: number, consignment_id: string) {
  if (goodsType === 2) {
    const accountId = await getAccountId(consignment_id)
    if (accountId)
      openNewWin(`https://www.aijx3.cn/w/${accountId}`)
    else
      errNotify('获取爱剑三账号ID失败')
  }
  else if (goodsType === 3) {
    const goodsId = await getGoodsId(consignment_id)
    if (goodsId)
      openNewWin(`https://www.aijx3.cn/g/${goodsId}`)
    else
      errNotify('获取爱剑三物品ID失败')
  }
}

export async function aijx3FollowRole() {
  let roleId = document.querySelector('.account_info .seq')?.textContent || ''
  if (!roleId)
    roleId = document.querySelector('.account-info-desc .seq')?.textContent || ''

  if (roleId) {
    const token = getLocalStorage('token', '')
    const res = await addFollowApiByToken(roleId, 2, token)
    if (res.code === 1) {
      successNotify('关注成功')
    }
    else if (res.msg === '抱歉，请先登录') {
      errNotify('关注失败，请先登录万宝楼')
      setTimeout(() => {
        window.open('https://jx3.seasunwbl.com', '_blank')
      }, 2000)
    }
    else {
      errNotify(res.msg)
    }
  }
  else {
    errNotify('获取商品编号失败')
  }
}
