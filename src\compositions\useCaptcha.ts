import { ref } from 'vue'
import { warningNotify } from '~/compositions/useNotify'
import type { GeetestObj } from '~/type'
import { getSessionStorage, handlingErrors, setSessionStorage } from '~/utils/utils'
import { orderCaptchaId, orderCaptchaTime } from '~/compositions/useSetting'
import initGeetest4 from '~/utils/gt'

interface LocalGeetestObj {
  geetestObj: GeetestObj
  expire: number
}
export const openZfbDialogCallback = ref<Function | null>(null)
const captchaLoading = ref(false)
const captchaState = ref(false)
const geetestObj = ref<LocalGeetestObj[]>([])
const captchaText = ref('请先完成验证码验证')
let timer: any = null

function startCountdown(count: number) {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  function countdown() {
    if (count < 0) {
      removeLocalCaptcha()
      return
    }
    // 修改captchaText为：当前验证码数量：${geetestObj.value.length}，验证码有效时间：${count} 秒
    captchaText.value = `当前验证码数量：${geetestObj.value.length} 个，验证码有效时间： ${count} 秒`
    count--
    timer = setTimeout(countdown, 1000)
  }
  countdown()
}

function getLocalCaptcha() {
  const localCaptcha = getSessionStorage('captchaList', []) as LocalGeetestObj[]
  if (localCaptcha.length > 0) {
    // 检查每个验证码是否过期
    const now = Date.now()
    const list = localCaptcha.filter(item => item.expire > now)
    if (list.length > 0) {
      geetestObj.value = list
      captchaState.value = true
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
      // 倒计时 10 min，以s为单位，每秒更改文本，初始文本为，已完成验证，有效期 600 秒
      const count = Math.floor((list[0].expire - new Date().getTime()) / 1e3)
      startCountdown(count)
    }
  }
}

function setLocalCaptcha(geetest: GeetestObj) {
  const now = Date.now()
  let list = getSessionStorage('captchaList', []) as LocalGeetestObj[]
  list = list.filter(item => item.expire > now)
  list.push({ geetestObj: geetest, expire: Date.now() + 60 * orderCaptchaTime * 1000 })
  geetestObj.value = list
  if (!timer) {
    const count = orderCaptchaTime * 60
    startCountdown(count)
  }
  setSessionStorage('captchaList', list)
}

function removeLocalCaptcha() {
  // 移除过期验证码
  const now = Date.now()
  const list = geetestObj.value.filter(item => item.expire > now)
  geetestObj.value = list
  // 如果没有验证码，则修改 captchaText.value = '请先完成验证码验证'
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  if (list.length === 0) {
    captchaState.value = false
    captchaText.value = '请先完成验证码验证'
  }
  else {
    const count = Math.floor((geetestObj.value[0].expire - new Date().getTime()) / 1e3)
    startCountdown(count)
  }
  setSessionStorage('captchaList', list)
  return list
}

/**
 * 验证码初始化
 * @param captchaObj 验证码对象
 */
const handlerEmbed = function (captchaObj: any) {
  captchaObj.onReady(() => {
    captchaLoading.value = false
  }).onSuccess(() => {
    setLocalCaptcha(captchaObj.getValidate())
    captchaState.value = true
    if (openZfbDialogCallback.value) {
      openZfbDialogCallback.value()
      openZfbDialogCallback.value = null
    }
  }).onError((e: any) => {
    captchaLoading.value = false
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox() // 显示验证码
}
let getCaptchaTimer: any = null
function initTimer() {
  if (getCaptchaTimer) {
    clearTimeout(getCaptchaTimer)
    getCaptchaTimer = null
  }
}
function loadCaptcha() {
  if (captchaLoading.value)
    return
  captchaLoading.value = true
  initTimer()

  initGeetest4({ product: 'bind', captchaId: orderCaptchaId }, handlerEmbed)
  captchaLoading.value = false
  initTimer()

  getCaptchaTimer = setTimeout(() => {
    if (captchaLoading.value === true) {
      warningNotify('验证码初始化超时，请重试。')
      captchaLoading.value = false
    }
  }, 6 * 1e3)
}

// 检查所有验证码过期的时间是否超过订单结束时间，如果超过则提示用户重新验证，传入一个时间戳
function captchaIsExpire(orderEndTime: number) {
  const list = removeLocalCaptcha() || []
  if (list.length === 0)
    return true

  return !list.some(item => item.expire > orderEndTime)
}

function getCaptcha() {
  if (geetestObj.value.length) {
    // 获取最早的验证码，并删除，如果过期则返回下一个
    const now = Date.now()
    const captcha = geetestObj.value.shift()
    // 删除本地存储的验证码
    removeLocalCaptcha()
    if (captcha && captcha.expire < now)
      return getCaptcha()
    else
      return captcha?.geetestObj
  }
  else {
    setSessionStorage('captchaList', [])
    return null
  }
}

export function useCaptcha() {
  getLocalCaptcha()
  return {
    loadCaptcha,
    captchaLoading,
    captchaState,
    geetestObj,
    captchaText,
    getCaptcha,
    captchaIsExpire,
    removeLocalCaptcha,
  }
}
