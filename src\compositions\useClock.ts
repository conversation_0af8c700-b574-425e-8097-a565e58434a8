import { ref } from 'vue'
import type { FollowItem } from '~/type'
import { successNotify, warningNotify } from '~/compositions/useNotify'
import { getSessionStorage, setSessionStorage } from '~/utils/utils'

const currClockInfos = ref(getSessionStorage('clockInfos', {}))
const currClockTimer = {} as { [key: string]: any }
export function deleteClockNotification(consignment_id: string) {
  if (currClockInfos.value?.[consignment_id]) {
    delete currClockInfos.value[consignment_id]
    setSessionStorage('clockInfos', currClockInfos.value)
  }
}

export function setClockNotification(row: FollowItem) {
  if (!currClockInfos.value?.[row.consignment_id]) {
    currClockInfos.value[row.consignment_id] = row
    setSessionStorage('clockInfos', currClockInfos.value)
  }
}

export function getClockIsExist(row: FollowItem) {
  const isExist = currClockInfos.value?.[row.consignment_id]
  if (isExist)
    row.clockTimer = currClockTimer?.[row.consignment_id] || null
  return isExist
}

export function checkLocalClockNotification() {
  currClockInfos.value = getSessionStorage('clockInfos', {})
  const now = new Date().getTime()
  for (const key in currClockInfos.value) {
    const row = currClockInfos.value[key]
    const clockTime = row?.end_time ? row.end_time - 2 * 60 * 1e3 : 0
    if (clockTime > now) {
      console.log('checkLocalClockNotification', row.goods_name, row.consignment_id, row.end_time, row.clockTimer)
      const delayInMilliseconds = clockTime - now
      const info = '公示期还有 2 分钟结束'
      sendNotification(row, info, delayInMilliseconds, true)
    }
    else {
      deleteClockNotification(row.consignment_id)
    }
  }
}

/**
 * 公示期通知
 * @param row 商品信息
 */
export function clockNotification(row: FollowItem) {
  if (row.clock) {
    row.clock = false
    if (row.clockTimer)
      clearTimeout(row.clockTimer)
    successNotify(`已取消 [ ${row.goods_name} ] 公示期提醒`)
    deleteClockNotification(row.consignment_id)
    return
  }
  const now = new Date().getTime()
  const clockTime = row?.end_time ? row.end_time - 2 * 60 * 1e3 : 0
  const isShowNotify = clockTime > now
  const delayInMilliseconds = clockTime - now
  const info = '公示期还有 2 分钟结束'
  if ('Notification' in window) {
    if (!isShowNotify) {
      if (now - clockTime > 2 * 60 * 1e3)
        warningNotify('公示期已结束，无法提醒')
      else
        warningNotify(`公示期仅剩${Math.floor((now - clockTime) / 1e3)}秒，请耐心等待`)
      return
    }
    if (Notification.permission === 'granted') {
      successNotify(`将在 ${new Date(new Date().getTime() + delayInMilliseconds).toLocaleString()} 时提醒您，请勿关闭该网页。`)
      sendNotification(row, info, delayInMilliseconds)
    }
    else if (Notification.permission !== 'denied') {
      // 请求通知权限
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          successNotify(`将在 ${new Date(new Date().getTime() + delayInMilliseconds).toLocaleString()} 时提醒您，请勿关闭该网页。`)
          sendNotification(row, info, delayInMilliseconds)
        }
        else {
          warningNotify('您拒绝了通知权限，将无法收到提醒')
        }
      })
    }
    else {
      warningNotify('您拒绝了通知权限，将无法收到提醒')
    }
  }
  else {
    warningNotify('浏览器不支持通知')
  }
}

function sendNotification(row: FollowItem, info: string, delayInMilliseconds: number, isLocalCheck = false) {
  const timeout = 30 * 1e3
  row.clock = true
  setClockNotification(row)
  const title = row.goods_name
  const icon = row.goods_icon_url
  let notification: any
  // 在 delayInMilliseconds 毫秒后显示通知
  const timer = setTimeout(() => {
    notification = new Notification(title, {
      body: info,
      icon, // 可选，指定通知图标
    })
    // 删除clockInfos中的row.consignment_id属性
    deleteClockNotification(row.consignment_id)
    // 设置定时器，在指定时间后关闭通知
    setTimeout(() => {
      notification.close()
    }, timeout)
  }, delayInMilliseconds)
  if (!isLocalCheck)
    row.clockTimer = timer
  else
    currClockTimer[row.consignment_id] = timer
}

/**
 * 公示期结束通知
 * @param info 商品名字
 * @param single_unit_price 商品单价
 * @param remaining_time 剩余时间
 * @param thumb 商品图标
 */
export function sendEndOfPeriodNotification(info: string, single_unit_price: number, remaining_time: number, thumb: string) {
  if ('Notification' in window) {
    if (Notification.permission === 'granted') {
      const notification = new Notification('公示期即将结束', {
        body: `商品：${info}，价格：${+single_unit_price / 100}，公示期剩余时间：${remaining_time} s`,
        icon: thumb, // 可选，指定通知图标
      })
      // 设置定时器，在指定时间后关闭通知
      setTimeout(() => {
        notification.close()
      }, 5 * 1e3)
    }
  }
}
