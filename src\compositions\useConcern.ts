import { ref } from 'vue'

import { Notify } from 'quasar'
import { setNoGoodsNameFollow } from '~/compositions/useNoGoodsNameFollow'
import { errNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import { isLogin } from '~/utils/utils'
import { setGoodsAutoFollow } from '~/compositions/useGoodsAutoFollow'
import { addFollowWithErrApi, getMyFollowList } from '~/api/wbl'
import type { GoodsDetail } from '~/type'
import { setPointFollow } from '~/compositions/usePointFollow'

const isShowConcernDialog = ref(false)
export const isStopFollow = ref(false)

const dismiss = ref<any>(null)
export function hideAutoFollowLoading(isStop = true, goodsName = '') {
  if (isStop)
    isStopFollow.value = false
  if (dismiss.value)
    dismiss.value()
  if (goodsName)
    warningNotify(`暂无符合条件的 [ ${goodsName} ]`)
}
export function showAutoFollowLoading(name = '', isStop = true) {
  isStopFollow.value = true
  if (isStop)
    isShowConcernDialog.value = false
  dismiss.value = Notify.create({
    spinner: true,
    message: name ? `自动关注 [ ${name} ] 中...` : '自动关注中...',
    color: 'teal',
    timeout: 0,
    actions: [
      { label: '停止关注', color: 'white', handler: () => {
        hideAutoFollowLoading()
        successNotify('已停止自动关注')
      } },
    ],
  })
}

export async function followGoods(goods: GoodsDetail) {
  const followRes = await addFollowWithErrApi(goods.consignment_id, 3)
  if (followRes.code === 1) {
    successNotify(`成功关注：${goods.info}，价格：${goods.single_unit_price / 100}，关注数：${goods.followed_num}`)
    await new Promise(resolve => setTimeout(resolve, 2000))
    return true
  }
  else {
    errNotify(`关注 [ ${goods.info} ] 失败：${followRes.msg}`)
    return false
  }
}

async function openConcernDialog() {
  if (await isLogin(true))
    isShowConcernDialog.value = true
}

function onConcernDialogClose() {
  setNoGoodsNameFollow()
  setGoodsAutoFollow()
  setPointFollow()
}

/**
 * 获取当前关注的外观数量
 */
export async function getCurrentFollowGoodsNum() {
  const followList = await getMyFollowList()
  return followList?.data?.follow?.[3]?.list?.length ?? 0
}

export function closeConcernDialog() {
  isShowConcernDialog.value = false
}
export function useConcern() {
  return { isShowConcernDialog, openConcernDialog, onConcernDialogClose }
}
