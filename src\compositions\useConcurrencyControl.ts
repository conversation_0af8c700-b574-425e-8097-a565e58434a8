import { computed, ref } from 'vue'
import { errNotify, warningNotify } from '~/compositions/useNotify'
import { recoverFromError } from '~/compositions/useErrorRecovery'

// 并发控制状态
const requestQueue = ref<Array<{
  id: string
  priority: number
  timestamp: number
  retryCount: number
  maxRetries: number
  execute: () => Promise<any>
  resolve: (value: any) => void
  reject: (error: any) => void
}>>([])

const activeRequests = ref(0)
const maxConcurrentRequests = ref(3) // 最大并发请求数
const baseDelay = ref(150) // 基础延迟时间（毫秒）
const networkLatency = ref(0) // 网络延迟
const serverResponseTime = ref(0) // 服务器响应时间

// 动态计算的延迟时间
const dynamicDelay = computed(() => {
  const latencyFactor = Math.max(1, networkLatency.value / 100)
  const responseFactor = Math.max(1, serverResponseTime.value / 500)
  const loadFactor = Math.max(1, activeRequests.value / maxConcurrentRequests.value)

  return Math.min(
    baseDelay.value * latencyFactor * responseFactor * loadFactor,
    5000, // 最大延迟5秒
  )
})

// 请求统计
const requestStats = ref({
  total: 0,
  success: 0,
  failed: 0,
  avgResponseTime: 0,
})

/**
 * 指数退避算法计算重试延迟
 */
function calculateBackoffDelay(retryCount: number, baseDelay: number = 1000): number {
  const jitter = Math.random() * 0.1 // 添加10%的随机抖动
  const delay = Math.min(baseDelay * 2 ** retryCount, 30000) // 最大30秒
  return delay * (1 + jitter)
}

/**
 * 更新网络性能指标
 */
function updateNetworkMetrics(responseTime: number, success: boolean) {
  requestStats.value.total++
  if (success) {
    requestStats.value.success++
  }
  else {
    requestStats.value.failed++
  }

  // 更新平均响应时间
  const totalResponseTime = requestStats.value.avgResponseTime * (requestStats.value.total - 1) + responseTime
  requestStats.value.avgResponseTime = totalResponseTime / requestStats.value.total

  // 更新服务器响应时间（使用移动平均）
  serverResponseTime.value = serverResponseTime.value * 0.8 + responseTime * 0.2

  // 根据成功率调整并发数
  const successRate = requestStats.value.success / requestStats.value.total
  if (successRate < 0.7 && maxConcurrentRequests.value > 1) {
    maxConcurrentRequests.value = Math.max(1, maxConcurrentRequests.value - 1)
    warningNotify(`检测到请求失败率较高，降低并发数至 ${maxConcurrentRequests.value}`)
  }
  else if (successRate > 0.9 && maxConcurrentRequests.value < 5) {
    maxConcurrentRequests.value = Math.min(5, maxConcurrentRequests.value + 1)
  }
}

/**
 * 智能请求排队和执行
 */
async function executeWithConcurrencyControl<T>(
  requestFn: () => Promise<T>,
  options: {
    priority?: number
    maxRetries?: number
    timeout?: number
    id?: string
  } = {},
): Promise<T> {
  const {
    priority = 1,
    maxRetries = 3,
    timeout = 30000,
    id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  } = options

  return new Promise((resolve, reject) => {
    const requestItem = {
      id,
      priority,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      execute: requestFn,
      resolve,
      reject,
    }

    // 按优先级插入队列
    const insertIndex = requestQueue.value.findIndex(item => item.priority < priority)
    if (insertIndex === -1)
      requestQueue.value.push(requestItem)
    else
      requestQueue.value.splice(insertIndex, 0, requestItem)

    // 尝试处理队列
    processQueue()

    // 设置超时
    setTimeout(() => {
      const index = requestQueue.value.findIndex(item => item.id === id)
      if (index !== -1) {
        requestQueue.value.splice(index, 1)
        reject(new Error('请求超时'))
      }
    }, timeout)
  })
}

/**
 * 处理请求队列
 */
async function processQueue() {
  if (activeRequests.value >= maxConcurrentRequests.value || requestQueue.value.length === 0)
    return

  const requestItem = requestQueue.value.shift()
  if (!requestItem)
    return

  activeRequests.value++

  try {
    const startTime = Date.now()

    // 如果是重试，应用指数退避延迟
    if (requestItem.retryCount > 0) {
      const backoffDelay = calculateBackoffDelay(requestItem.retryCount - 1)
      await new Promise(resolve => setTimeout(resolve, backoffDelay))
    }
    else {
      // 首次请求，应用动态延迟
      await new Promise(resolve => setTimeout(resolve, dynamicDelay.value))
    }

    const result = await requestItem.execute()
    const responseTime = Date.now() - startTime

    updateNetworkMetrics(responseTime, true)
    requestItem.resolve(result)
  }
  catch (error) {
    const responseTime = Date.now() - requestItem.timestamp
    updateNetworkMetrics(responseTime, false)

    // 尝试错误恢复
    const recovered = await recoverFromError(error)

    // 检查是否需要重试
    if (requestItem.retryCount < requestItem.maxRetries) {
      requestItem.retryCount++

      // 如果错误恢复成功，优先级提高
      if (recovered)
        requestItem.priority = Math.min(requestItem.priority + 1, 5)

      // 重新加入队列进行重试
      const insertIndex = requestQueue.value.findIndex(item => item.priority < requestItem.priority)
      if (insertIndex === -1)
        requestQueue.value.push(requestItem)
      else
        requestQueue.value.splice(insertIndex, 0, requestItem)
    }
    else {
      requestItem.reject(error)
    }
  }
  finally {
    activeRequests.value--

    // 继续处理队列
    setTimeout(processQueue, 10)
  }
}

/**
 * 批量执行请求
 */
async function executeBatch<T>(
  requests: Array<() => Promise<T>>,
  options: {
    batchSize?: number
    priority?: number
    maxRetries?: number
  } = {},
): Promise<T[]> {
  const { batchSize = 3, priority = 1, maxRetries = 3 } = options
  const results: T[] = []

  for (let i = 0; i < requests.length; i += batchSize) {
    const batch = requests.slice(i, i + batchSize)
    const batchPromises = batch.map((request, index) =>
      executeWithConcurrencyControl(request, {
        priority,
        maxRetries,
        id: `batch_${i + index}_${Date.now()}`,
      }),
    )

    const batchResults = await Promise.allSettled(batchPromises)

    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results[i + index] = result.value
      }
      else {
        console.error(`批量请求 ${i + index} 失败:`, result.reason)
        throw result.reason
      }
    })
  }

  return results
}

/**
 * 获取并发控制状态
 */
function getConcurrencyStatus() {
  return {
    activeRequests: activeRequests.value,
    queueLength: requestQueue.value.length,
    maxConcurrent: maxConcurrentRequests.value,
    dynamicDelay: dynamicDelay.value,
    stats: requestStats.value,
    networkLatency: networkLatency.value,
    serverResponseTime: serverResponseTime.value,
  }
}

/**
 * 重置并发控制状态
 */
function resetConcurrencyControl() {
  requestQueue.value = []
  activeRequests.value = 0
  requestStats.value = {
    total: 0,
    success: 0,
    failed: 0,
    avgResponseTime: 0,
  }
  networkLatency.value = 0
  serverResponseTime.value = 0
  maxConcurrentRequests.value = 3
}

export function useConcurrencyControl() {
  return {
    executeWithConcurrencyControl,
    executeBatch,
    getConcurrencyStatus,
    resetConcurrencyControl,
    dynamicDelay,
    requestStats: computed(() => requestStats.value),
    activeRequests: computed(() => activeRequests.value),
    queueLength: computed(() => requestQueue.value.length),
  }
}
