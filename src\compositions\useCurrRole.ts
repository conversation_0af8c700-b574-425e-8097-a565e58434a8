import { ref } from 'vue'
import { getCurrRoleInfoApi, loginByToken } from '~/api/pyapi'
import type { CategoryInfoWithCountMap, TrinketCategoryItem } from '~/type'
import { isLogin, setSessionStorage } from '~/utils/utils'
import { getAppearanceMapTokenApi, getBaseInfoApi, getBaseInfoApiSafe } from '~/api/wbl'
import { errNotify } from '~/compositions/useNotify'
import { setToken } from '~/compositions/useMultiOpen'
import { isMaintenanceRef } from '~/compositions/useSetting'

const isShowCurrRole = ref(false) // 是否显示当前角色
const appearanceBoxScore = ref(0) // 外观收藏分
const categoryInfoWithCountMap = ref<CategoryInfoWithCountMap | null>(null) // 分类信息
const trinketCategoryItems = ref<TrinketCategoryItem[]>([]) // 饰品分类
const isReady = ref(false) // 是否已经获取过数据

async function getInfo() {
  if (!(await isLogin()))
    return
  const res = await getBaseInfoApi()
  if (res?.code === 1) {
    isShowCurrRole.value = true
    const { zone_name, server_name, role_name } = res.data.base_info
    const tokenRes = await getAppearanceMapTokenApi()
    const { token } = tokenRes?.data
    await loginByToken(token)
    const roleRes = await getCurrRoleInfoApi(zone_name, server_name, role_name)
    const { result } = roleRes.data
    appearanceBoxScore.value = result.appearanceBoxScore
    categoryInfoWithCountMap.value = result.categoryInfoWithCountMap
    trinketCategoryItems.value = result.trinketCategoryItems
    isReady.value = true
    setSessionStorage('currToken', JSON.stringify(res.data.ts_session_id))
  }
  else {
    errNotify(res?.msg)
  }
}
export async function showCurrRole() {
  if (isReady.value)
    isShowCurrRole.value = true
  await getInfo()
}

export async function setCurrRole(token = '', isAccountSwitch = false) {
  // 如果是账号切换，使用安全的API调用，不会清理主要的登录cookie
  const baseInfoRes = isAccountSwitch
    ? await getBaseInfoApiSafe(token)
    : await getBaseInfoApi(token)

  try {
    let base_info
    let ts_session_id
    if (baseInfoRes.code === 1) {
      base_info = baseInfoRes?.data?.base_info
      ts_session_id = baseInfoRes?.data?.ts_session_id
    }
    else if (baseInfoRes.msg?.includes('维护中')) {
      isMaintenanceRef.value = true
    }
    else {
      base_info = baseInfoRes?.data
      ts_session_id = baseInfoRes?.data?.ts_session_id
    }
    if (base_info) {
      setSessionStorage('baseInfo', base_info)
      setToken(ts_session_id, base_info?.account)
    }
  }
  catch (e) {
    setSessionStorage('baseInfo', {})
    console.error(e)
  }
}

export function useCurrRole() {
  return {
    isShowCurrRole,
    appearanceBoxScore,
    categoryInfoWithCountMap,
    isReady,
    trinketCategoryItems,
  }
}
