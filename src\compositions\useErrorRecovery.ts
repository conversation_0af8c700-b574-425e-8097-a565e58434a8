import { ref } from 'vue'
import { errNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import { loadAccountCaptcha, multiGeetestObj } from '~/compositions/useMultiOpen'
import { useCaptcha } from '~/compositions/useCaptcha'

// 获取验证码相关功能
const { loadCaptcha } = useCaptcha()

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'network_error',
  CAPTCHA_ERROR = 'captcha_error',
  ACCOUNT_ERROR = 'account_error',
  SERVER_ERROR = 'server_error',
  VALIDATION_ERROR = 'validation_error',
  TIMEOUT_ERROR = 'timeout_error',
  UNKNOWN_ERROR = 'unknown_error',
}

// 错误恢复状态
const recoveryState = ref({
  isRecovering: false,
  currentError: null as any,
  recoveryAttempts: 0,
  maxRecoveryAttempts: 3,
  lastRecoveryTime: 0,
})

// 错误统计
const errorStats = ref({
  networkErrors: 0,
  captchaErrors: 0,
  accountErrors: 0,
  serverErrors: 0,
  validationErrors: 0,
  timeoutErrors: 0,
  unknownErrors: 0,
  totalErrors: 0,
  recoveredErrors: 0,
})

/**
 * 分析错误类型
 */
function analyzeError(error: any): ErrorType {
  const errorMessage = error?.message || error?.msg || String(error)
  const errorCode = error?.code

  // 网络错误
  if (errorMessage.includes('网络') || errorMessage.includes('Network')
    || errorMessage.includes('fetch') || errorMessage.includes('timeout')
    || errorCode === -1 || errorCode === 0)
    return ErrorType.NETWORK_ERROR

  // 验证码错误
  if (errorMessage.includes('验证码') || errorMessage.includes('captcha')
    || errorMessage.includes('geetest') || errorCode === -2)
    return ErrorType.CAPTCHA_ERROR

  // 账号相关错误
  if (errorMessage.includes('登录') || errorMessage.includes('账号')
    || errorMessage.includes('token') || errorMessage.includes('session')
    || errorCode === -3)
    return ErrorType.ACCOUNT_ERROR

  // 服务器错误
  if (errorMessage.includes('服务器') || errorMessage.includes('Server')
    || errorMessage.includes('500') || errorMessage.includes('502')
    || errorMessage.includes('503') || errorCode >= 500)
    return ErrorType.SERVER_ERROR

  // 验证错误
  if (errorMessage.includes('参数') || errorMessage.includes('格式')
    || errorMessage.includes('validation') || errorCode === 400)
    return ErrorType.VALIDATION_ERROR

  // 超时错误
  if (errorMessage.includes('超时') || errorMessage.includes('timeout'))
    return ErrorType.TIMEOUT_ERROR

  return ErrorType.UNKNOWN_ERROR
}

/**
 * 更新错误统计
 */
function updateErrorStats(errorType: ErrorType, recovered: boolean = false) {
  errorStats.value.totalErrors++

  switch (errorType) {
    case ErrorType.NETWORK_ERROR:
      errorStats.value.networkErrors++
      break
    case ErrorType.CAPTCHA_ERROR:
      errorStats.value.captchaErrors++
      break
    case ErrorType.ACCOUNT_ERROR:
      errorStats.value.accountErrors++
      break
    case ErrorType.SERVER_ERROR:
      errorStats.value.serverErrors++
      break
    case ErrorType.VALIDATION_ERROR:
      errorStats.value.validationErrors++
      break
    case ErrorType.TIMEOUT_ERROR:
      errorStats.value.timeoutErrors++
      break
    default:
      errorStats.value.unknownErrors++
  }

  if (recovered) {
    errorStats.value.recoveredErrors++
  }
}

/**
 * 验证码错误恢复策略
 */
async function recoverFromCaptchaError(): Promise<boolean> {
  try {
    warningNotify('检测到验证码错误，正在自动重新获取验证码...')

    // 检查当前验证码数量
    const currentCaptchaCount = multiGeetestObj.value.length

    if (currentCaptchaCount === 0) {
      // 重新加载验证码
      await loadAccountCaptcha()

      // 等待验证码加载完成
      let waitTime = 0
      const maxWaitTime = 10000 // 最大等待10秒

      while (multiGeetestObj.value.length === 0 && waitTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, 500))
        waitTime += 500
      }

      if (multiGeetestObj.value.length > 0) {
        successNotify('验证码重新获取成功')
        return true
      }
      else {
        errNotify('验证码重新获取失败，请手动重试')
        return false
      }
    }
    else {
      // 已有验证码，可能是验证码过期，重新加载
      await loadCaptcha()
      return true
    }
  }
  catch (error) {
    console.error('验证码恢复失败:', error)
    errNotify('验证码恢复失败')
    return false
  }
}

/**
 * 网络错误恢复策略
 */
async function recoverFromNetworkError(): Promise<boolean> {
  try {
    warningNotify('检测到网络错误，正在尝试恢复...')

    // 等待一段时间后重试
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 可以在这里添加网络连接检测
    // 简单的网络检测：尝试访问一个轻量级的API
    try {
      const response = await fetch('https://trade-api.seasunwbl.com/api/platform/captcha/pre_auth', {
        method: 'HEAD',
        timeout: 5000,
      })

      if (response.ok) {
        successNotify('网络连接已恢复')
        return true
      }
    }
    catch {
      // 网络仍然有问题
    }

    errNotify('网络连接仍然存在问题，请检查网络设置')
    return false
  }
  catch (error) {
    console.error('网络错误恢复失败:', error)
    return false
  }
}

/**
 * 账号错误恢复策略
 */
async function recoverFromAccountError(): Promise<boolean> {
  try {
    warningNotify('检测到账号错误，建议重新登录')

    // 这里可以触发重新登录流程
    // 或者提示用户手动重新登录

    return false // 账号错误通常需要用户手动处理
  }
  catch (error) {
    console.error('账号错误恢复失败:', error)
    return false
  }
}

/**
 * 服务器错误恢复策略
 */
async function recoverFromServerError(): Promise<boolean> {
  try {
    warningNotify('检测到服务器错误，正在等待服务器恢复...')

    // 服务器错误通常需要等待一段时间
    await new Promise(resolve => setTimeout(resolve, 5000))

    return true // 假设服务器错误是临时的
  }
  catch (error) {
    console.error('服务器错误恢复失败:', error)
    return false
  }
}

/**
 * 主要的错误恢复函数
 */
export async function recoverFromError(error: any): Promise<boolean> {
  if (recoveryState.value.isRecovering)
    return false // 已经在恢复中，避免重复恢复

  const now = Date.now()
  const timeSinceLastRecovery = now - recoveryState.value.lastRecoveryTime

  // 如果距离上次恢复时间太短，跳过恢复
  if (timeSinceLastRecovery < 5000)
    return false

  recoveryState.value.isRecovering = true
  recoveryState.value.currentError = error
  recoveryState.value.recoveryAttempts++
  recoveryState.value.lastRecoveryTime = now

  try {
    const errorType = analyzeError(error)
    updateErrorStats(errorType)

    let recovered = false

    switch (errorType) {
      case ErrorType.CAPTCHA_ERROR:
        recovered = await recoverFromCaptchaError()
        break
      case ErrorType.NETWORK_ERROR:
        recovered = await recoverFromNetworkError()
        break
      case ErrorType.ACCOUNT_ERROR:
        recovered = await recoverFromAccountError()
        break
      case ErrorType.SERVER_ERROR:
        recovered = await recoverFromServerError()
        break
      case ErrorType.TIMEOUT_ERROR:
        // 超时错误通常可以直接重试
        recovered = true
        break
      default:
        // 未知错误，尝试通用恢复策略
        warningNotify('检测到未知错误，正在尝试通用恢复策略...')
        await new Promise(resolve => setTimeout(resolve, 1000))
        recovered = true
    }

    if (recovered) {
      updateErrorStats(errorType, true)
      successNotify('错误恢复成功')
    }

    return recovered
  }
  catch (recoveryError) {
    console.error('错误恢复过程中出现异常:', recoveryError)
    errNotify('错误恢复失败')
    return false
  }
  finally {
    recoveryState.value.isRecovering = false
  }
}

/**
 * 获取错误恢复状态
 */
export function getRecoveryStatus() {
  return {
    isRecovering: recoveryState.value.isRecovering,
    recoveryAttempts: recoveryState.value.recoveryAttempts,
    errorStats: errorStats.value,
  }
}

/**
 * 重置错误恢复状态
 */
export function resetRecoveryState() {
  recoveryState.value = {
    isRecovering: false,
    currentError: null,
    recoveryAttempts: 0,
    maxRecoveryAttempts: 3,
    lastRecoveryTime: 0,
  }

  errorStats.value = {
    networkErrors: 0,
    captchaErrors: 0,
    accountErrors: 0,
    serverErrors: 0,
    validationErrors: 0,
    timeoutErrors: 0,
    unknownErrors: 0,
    totalErrors: 0,
    recoveredErrors: 0,
  }
}

export function useErrorRecovery() {
  return {
    recoverFromError,
    getRecoveryStatus,
    resetRecoveryState,
    analyzeError,
    ErrorType,
  }
}
