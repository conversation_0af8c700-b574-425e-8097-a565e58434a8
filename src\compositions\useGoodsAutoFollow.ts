import { ref } from 'vue'
import type { GoodsDetail, TableColumns } from '~/type'
import { getLocalStorage, setLocalStorage } from '~/utils/utils'
import {
  followGoods,
  getCurrentFollowGoodsNum,
  hideAutoFollowLoading,
  isStopFollow,
  showAutoFollowLoading,
} from '~/compositions/useConcern'
import { errNotify, successNotify } from '~/compositions/useNotify'
import { getGoodsListApi } from '~/api/wbl'
import { GoodsState } from '~/api/enums'

interface GoodsRow {
  name: string
  price: number
  action: string
}
const maxPublicTime = ref<number>(2)
const minPublicTime = ref<number>(0)
const columns: TableColumns<GoodsRow>[] = [
  { name: 'name', label: '外观名称', align: 'left', field: 'name', headerStyle: 'min-width: 100px' },
  { name: 'price', label: '预期价格', field: 'price', headerStyle: 'min-width: 100px' },
  { name: 'action', label: '操作', field: 'action', align: 'center', headerStyle: 'width: 80px' },
]
const rows = ref<GoodsRow[]>([])

/**
 * 初始化
 */
function init() {
  const goodsAutoFollow = getLocalStorage('goodsAutoFollow', {})
  maxPublicTime.value = goodsAutoFollow.maxPublicTime || 2
  minPublicTime.value = goodsAutoFollow.minPublicTime || 0
  rows.value = goodsAutoFollow.rows || []
}

/**
 * 添加商品行
 */
function addGoodsRow() {
  rows.value.push({ name: '', price: 0, action: '' })
}

export function addFollowGoodsRowByNameAndPrice(name: string, price: number) {
  // 先获取本地存储的数据
  const goodsAutoFollow = getLocalStorage('goodsAutoFollow', {})
  const followGoods = goodsAutoFollow.rows || []
  const names = followGoods.map((row: GoodsRow) => row.name)
  if (names.includes(name)) {
    errNotify(`外观 [ ${name} ] 已存在`)
    return
  }
  followGoods.push({ name, price, action: '' })
  rows.value = followGoods
  // 设置数据到本地存储
  setGoodsAutoFollow()
  successNotify(`添加外观 [ ${name} ] 成功`)
}

function removeGoodsRow(index: number) {
  rows.value.splice(index, 1)
}

/**
 * 外观名称变化
 * @param value 变化后的行数据
 */
function onNameChange(value: GoodsRow) {
  // 对外观名称进行处理。如果外观名称的行数超过5行，则只显示前5行，如果有空白行则删除，每行去除前后空格
  // 先去除前后空格以及空白行
  const names = value.name.split('\n').map(name => name.trim()).filter(name => name)
  // 过滤重复的行
  const namesTemp: string[] = []
  names.forEach((name) => {
    if (!namesTemp.includes(name))
      namesTemp.push(name)
  })
  // 如果行数超过5行，则只显示前5行
  if (namesTemp.length > 5)
    value.name = namesTemp.slice(0, 5).join('\n')
  else
    value.name = namesTemp.join('\n')
}

/**
 * 设置数据到本地存储
 */
export function setGoodsAutoFollow() {
  // 处理rows，如果name为空则删除，如果name.split('\n').length > 5则只保留前5行
  const rowsTemp = rows.value.filter((row) => {
    const names = row.name.split('\n').map(name => name.trim()).filter(name => name)
    return names.length > 0
  }).map((row) => {
    const names = row.name.split('\n').map(name => name.trim()).filter(name => name)
    if (names.length > 5)
      row.name = names.slice(0, 5).join('\n')
    else
      row.name = names.join('\n')
    return row
  })
  if (minPublicTime.value < maxPublicTime.value) {
    setLocalStorage('goodsAutoFollow', {
      maxPublicTime: maxPublicTime.value,
      minPublicTime: minPublicTime.value,
      rows: rowsTemp,
    })
  }
  else {
    const goodsAutoFollow = getLocalStorage('goodsAutoFollow', {})
    maxPublicTime.value = goodsAutoFollow.maxPublicTime || 2
    minPublicTime.value = goodsAutoFollow.minPublicTime || 0
    setLocalStorage('goodsAutoFollow', {
      maxPublicTime: maxPublicTime.value,
      minPublicTime: minPublicTime.value,
      rows: rowsTemp,
    })
  }
}

/**
 * 是否关注商品
 * @param goods 商品信息
 * @param price 预期商品价格
 */
async function isFollowGoods(goods: GoodsDetail, price: number) {
  return (
    !goods.is_followed
    && goods.single_unit_price <= price * 100
    && ((goods.remaining_time <= maxPublicTime.value * 60 * 60 && goods.remaining_time >= minPublicTime.value * 60 * 60) || goods.state === 5)
  )
}

async function autoFollow(callback: (currFollowNum: number) => Promise<number>) {
  if (isStopFollow.value) {
    errNotify('正在自动关注，请勿重复操作')
    return
  }
  const currentFollowGoodsNum = await getCurrentFollowGoodsNum()
  if (currentFollowGoodsNum >= 20) {
    errNotify(`已经达到最大关注数 20 个，已停止关注`)
    return
  }
  await callback(currentFollowGoodsNum)
  isStopFollow.value = false
}

export async function autoFollowSingle(goodsName: string, price: number) {
  await autoFollow(async currFollowNum => await autoFollowGoodsByName(goodsName, price, currFollowNum, false))
}

export async function autoFollowAll() {
  await autoFollow(async (currFollowNum) => {
    for (const row of rows.value) {
      currFollowNum = await autoFollowGoodsByName(row.name, row.price, currFollowNum, true)
      if (currFollowNum >= 20 || isStopFollow.value === false)
        return currFollowNum
      await new Promise(resolve => setTimeout(resolve, 3000))
    }
    return currFollowNum
  })
}

async function autoFollowGoodsByName(goodsName: string, price: number, currFollowNum: number, isStop = true) {
  goodsName = goodsName.split('\n').join(',')
  showAutoFollowLoading(goodsName, isStop)
  let page = 1
  let currentFollowGoodsNum = currFollowNum
  // 循环获取商品列表
  try {
    while (isStopFollow.value) {
      // 如果关注的商品数量超过10，则停止关注
      if (currentFollowGoodsNum >= 20) {
        errNotify(`已经达到最大关注数 20 个，停止关注`)
        hideAutoFollowLoading(false)
        return currentFollowGoodsNum
      }
      const res = await getGoodsListApi(GoodsState.All, goodsName, page, { str: 'price', num: 1 })
      if (res?.data?.list?.length === 0) {
        hideAutoFollowLoading(false, currFollowNum === currentFollowGoodsNum ? goodsName : '')
        return currentFollowGoodsNum
      }
      const goodsList = res.data.list as GoodsDetail[]
      for (const goods of goodsList) {
        if (await isFollowGoods(goods, price)) {
          const isFollow = await followGoods(goods)
          if (isFollow) {
            currentFollowGoodsNum++
            if (currentFollowGoodsNum >= 20) {
              errNotify(`已经达到最大关注数 20 个，停止关注`)
              hideAutoFollowLoading(false)
              return currentFollowGoodsNum
            }
          }
        }
        if (goods.single_unit_price > price * 100) {
          hideAutoFollowLoading(false, currFollowNum === currentFollowGoodsNum ? goodsName : '')
          return currentFollowGoodsNum
        }
      }

      if (page % 4 === 0)
        await new Promise(resolve => setTimeout(resolve, 2000))

      page++
    }
    hideAutoFollowLoading(false, currFollowNum === currentFollowGoodsNum ? goodsName : '')
    return currentFollowGoodsNum
  }
  catch (e) {
    hideAutoFollowLoading(false, currFollowNum === currentFollowGoodsNum ? goodsName : '')
    return currentFollowGoodsNum
  }
}

export default function useGoodsAutoFollow() {
  init()
  return {
    onNameChange,
    addGoodsRow,
    maxPublicTime,
    minPublicTime,
    columns,
    rows,
    removeGoodsRow,
  }
}
