import type { TradeData } from '~/type'
import { getDetailInfoApi, getGoodsListApi, getTradeDataApi } from '~/api/wbl'
import { GM_getValue, GM_setValue } from '$'
import { GoodsState } from '~/api/enums'

interface LocalGoodsInfo {
  // 30日最低均价
  minAvgPrice: number
  // 最后检查时间，格式为 'yyyy-MM-dd'
  lastCheckTime?: string
  // 30日交易热度
  sellCount: number
  // 30日实装热度
  followHeat: number
  // 30日均价
  avg_price_30: number
  // 当天均价
  today_avg_price: number
}

interface LocalGoodsInfoList {
  [goodsName: string]: LocalGoodsInfo
}

interface GoodsInfo {
  // 30日最低均价
  minAvgPrice: number
  // 30日交易热度
  sellCount: number
  // 30日实装热度
  followHeat: number
  // 在售期最低价
  minSalePrice: number
  // 公示期最低价
  minPublicPrice: number
}

/**
 * 获取本地商品信息
 * @param goodsName
 */
export function getLocalGoodsInfo(goodsName: string): LocalGoodsInfo | undefined {
  const goodsInfo = GM_getValue('goodsInfo', {}) as LocalGoodsInfoList
  return goodsInfo?.[goodsName]
}

/**
 * 保存商品信息到本地
 * @param goodsName 商品名
 * @param goodsInfo 商品信息
 */
function setLocalGoodsInfo(goodsName: string, goodsInfo: LocalGoodsInfo) {
  const goodsInfoList = GM_getValue('goodsInfo', {}) as LocalGoodsInfoList
  goodsInfoList[goodsName] = goodsInfo
  GM_setValue('goodsInfo', goodsInfoList)
}

/**
 * 获取商品交易数据
 * @param goodsName 商品名
 * @param consignment_id 商品ID
 */
async function getTradeData(goodsName: string, consignment_id: string): Promise<LocalGoodsInfo> {
  const localGoodsInfo = getLocalGoodsInfo(goodsName)
  // 如果是同一天，直接返回，不再请求，减少请求次数
  if (localGoodsInfo?.lastCheckTime === new Date().toLocaleDateString()) {
    return {
      minAvgPrice: localGoodsInfo.minAvgPrice,
      sellCount: localGoodsInfo.sellCount,
      followHeat: localGoodsInfo.followHeat,
      avg_price_30: localGoodsInfo.avg_price_30,
      today_avg_price: localGoodsInfo.today_avg_price,
    }
  }
  const res = await getTradeDataApi(consignment_id)
  if (!res?.data)
    return { minAvgPrice: 0, sellCount: 0, followHeat: 0, today_avg_price: 0, avg_price_30: 0 }
  const tradeData = res?.data as TradeData
  let priceArr = [...tradeData.trend_list.map(trend => trend.avg_price), tradeData.avg_price_30]
  priceArr = priceArr.filter(item => item !== 0)
  const minAvgPrice = priceArr.length === 0 ? 0 : Math.min(...priceArr)
  const avg_price_30 = tradeData.avg_price_30
  const today_avg_price = tradeData?.trend_list[tradeData?.trend_list?.length - 1]?.avg_price ?? 0
  // 保存到本地
  setLocalGoodsInfo(goodsName, {
    minAvgPrice,
    lastCheckTime: new Date().toLocaleDateString(),
    sellCount: tradeData.sell_cnt_30,
    followHeat: tradeData.follow_heat_30,
    avg_price_30,
    today_avg_price,
  })
  return { minAvgPrice, sellCount: tradeData.sell_cnt_30, followHeat: tradeData.follow_heat_30, avg_price_30, today_avg_price }
}

/**
 * 获取30日最低均价
 * @param goodsName 商品名
 * @param consignment_id 商品ID
 */
export async function getMinAvgPrice(goodsName: string, consignment_id: string) {
  const res = await getTradeData(goodsName, consignment_id)
  return res.minAvgPrice
}

/**
 * 获取今日均价
 * @param goodsName
 * @param consignment_id
 */
export async function getNowAvgPrice(goodsName: string, consignment_id: string) {
  const res = await getTradeData(goodsName, consignment_id)
  return res.today_avg_price
}

/**
 * 获取30日均价
 * @param goodsName
 * @param consignment_id
 */
export async function get30AvgPrice(goodsName: string, consignment_id: string) {
  const res = await getTradeData(goodsName, consignment_id)
  return res.avg_price_30
}

/**
 * 获取商品关注数和itemId
 * @param consignment_id 商品ID
 * @param goodsType 商品类型 2:角色 3：外观
 */
export async function getGoodsFollowedNumAndItemId(consignment_id: string, goodsType: number) {
  const res = await getDetailInfoApi(consignment_id, goodsType)
  if (res?.data) {
    return {
      followed_num: res.data.followed_num,
      itemId: res.data.attrs?.item_index ?? 0,
    }
  }
  else {
    return {
      followed_num: 0,
      itemId: 0,
    }
  }
}

/**
 * 获取商品在售期最低价
 * @param goodsName 商品名
 */
async function getMinSalePrice(goodsName: string) {
  const saleList = await getGoodsListApi(GoodsState.Sale, goodsName, 1, { str: 'price', num: 1 })
  return saleList?.data?.list?.[0]?.single_unit_price || 0
}

/**
 * 获取商品公示期最低价
 * @param goodsName 商品名
 */
async function getMinPublicPrice(goodsName: string) {
  const publicList = await getGoodsListApi(GoodsState.Publicity, goodsName, 1, { str: 'price', num: 1 })
  return publicList?.data?.list?.[0]?.single_unit_price || 0
}

/**
 * 获取商品信息(外观)
 * @param goodsName 商品名
 * @param consignment_id 商品ID
 */
export async function getGoodsInfo(goodsName: string, consignment_id: string): Promise<GoodsInfo> {
  return Promise.all([
    getTradeData(goodsName, consignment_id),
    getMinSalePrice(goodsName),
    getMinPublicPrice(goodsName),
  ]).then(([tradeData, minSalePrice, minPublicPrice]) => ({
    ...tradeData,
    minSalePrice,
    minPublicPrice,
  }))
}
