import { ref } from 'vue'
import { cancelOrderApi, getOrderListApi } from '~/api/wbl'
import type { GoodsOrder, TableColumns } from '~/type'
import { isLogin } from '~/utils/utils'

const columns: TableColumns<GoodsOrder>[] = [
  { name: 'goods_list', label: '商品信息', align: 'left', field: 'goods_list' },
  { name: 'money', label: '订单总价', align: 'right', field: 'money', format: val => `${val / 100} 元` },
  { name: 'state', label: '订单状态', align: 'left', field: 'state' },
  { name: 'date', label: '下单时间', align: 'left', field: 'date' },
  { name: 'order_id', label: '订单号', align: 'left', field: 'order_id' },
  { name: 'action', label: '操作', align: 'left', field: 'action' },
]
const isShow = ref(false)
const currOrderType = ref(2)
const currPage = ref(1)
const orderList = ref<GoodsOrder[]>([])
const loading = ref(false)
const isEnd = ref(false)
async function getOrders() {
  if (loading.value)
    return
  loading.value = true
  const res = await getOrderListApi(currPage.value, currOrderType.value)
  const list = res?.data?.list as GoodsOrder[] || []
  if (list.length === 0) {
    if (currPage.value > 1) {
      currPage.value--
    }
    isEnd.value = true
    loading.value = false
    return
  }
  orderList.value = list
  loading.value = false
}

export async function openGoodsOrder() {
  if (await isLogin()) {
    isShow.value = true
    if (orderList.value.length === 0)
      await getOrders()
  }
}

async function firstPage() {
  currPage.value = 1
  isEnd.value = false
  await getOrders()
}

async function prevPage() {
  if (currPage.value === 1)
    return
  currPage.value--
  isEnd.value = false
  await getOrders()
}

async function nextPage() {
  if (isEnd.value)
    return
  currPage.value++
  await getOrders()
}

async function toggleOrderType() {
  currPage.value = 1
  isEnd.value = false
  await getOrders()
}

function cancelOrder(orderId: string) {
  cancelOrderApi(orderId, currOrderType.value).then(
    async () => {
      await getOrders()
    },
  )
}

export function useGoodsOrder() {
  return {
    columns,
    isShow,
    currOrderType,
    currPage,
    orderList,
    loading,
    isEnd,
    prevPage,
    nextPage,
    getOrders,
    toggleOrderType,
    firstPage,
    cancelOrder,
  }
}
