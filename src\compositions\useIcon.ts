// @unocss-include
const myIcons = [
  'i-pixelarticons-arrow-up',
  'i-pixelarticons-arrow-down',
  'i-pixelarticons-play',
  'i-pixelarticons-repeat',
  'i-pixelarticons-zoom-out',
  'i-pixelarticons-zoom-in',
  'i-pixelarticons-close',
  'i-pixelarticons-coin',
  'i-pixelarticons-heart',
  'i-pixelarticons-downasaur',
  'i-pixelarticons-checkbox',
  'i-pixelarticons-checkbox-on',
  'i-pixelarticons-zap',
  'i-pixelarticons-shopping-bag',
  'i-pixelarticons-gift',
  'i-pixelarticons-notes-delete',
  'i-pixelarticons-check',
  'i-pixelarticons-trash',
]
const replaceIcon = {
  arrow_upward: 'i-pixelarticons-arrow-up',
  arrow_drop_down: 'i-pixelarticons-chevron-down',
  chevron_right: 'i-pixelarticons-chevron-right',
  chevron_left: 'i-pixelarticons-chevron-left',
  first_page: 'i-pixelarticons-prev',
  last_page: 'i-pixelarticons-next',
  error: 'i-pixelarticons-alert',
  cancel: 'i-pixelarticons-close',
}

const imgIcons = {
  jx3_yuan: 'img:https://jx3wbl.xoyocdn.com/img/icon-rmb.c3ce8bfb.png',
}

type replaceIconType = keyof typeof replaceIcon

export function useMyIcon(iconName: string) {
  const reIcon = replaceIcon[iconName as replaceIconType]
  if (reIcon !== void 0)
    return { cls: reIcon }
  const imgIcon = imgIcons[iconName as keyof typeof imgIcons]
  if (imgIcon !== void 0)
    return { icon: imgIcon }
  const icon = myIcons.find(el => el === iconName)
  if (icon !== void 0)
    return { cls: icon }
}
