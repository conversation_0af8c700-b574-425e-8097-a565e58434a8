import { Screen } from 'quasar'
import { getLocalStorage, getSessionStorage, openNewWin, setSessionStorage } from '~/utils/utils'
import { getJx3ShAccountDetailsApi, getJx3ShAccountIDApi } from '~/api/jx3sh'
import { errNotify, successNotify } from '~/compositions/useNotify'
import { addFollowApiByToken } from '~/api/wbl'

async function getAccountId(consignment_id: string) {
  const accountIds = getSessionStorage('jx3ShAccountIds', {})
  if (accountIds?.[consignment_id])
    return accountIds[consignment_id]
  const res = await getJx3ShAccountDetailsApi(consignment_id) as any
  if (res?.data?.goods?.length) {
    accountIds[consignment_id] = res.data.goods[0].id
    setSessionStorage('jx3ShAccountIds', accountIds)
    return accountIds[consignment_id]
  }
  else {
    return ''
  }
}

export async function jx3shDetail(consignment_id: string) {
  const accountId = await getAccountId(consignment_id)
  if (accountId) {
    if (Screen.xs)
      openNewWin(`https://jx3sh.com/h5/pages/goods/wbl?type=wbl&id=${accountId}`)
    else
      openNewWin(`https://pc.jx3sh.com/#/detail/wbl/${accountId}`)
  }
  else {
    errNotify('获取剑网3商会账号ID失败')
  }
}

export async function j3shFollowRole() {
  const search = window.location.search // '?type=wbl&id=1440004'
  const hash = window.location.hash // '#/detail/wbl/1440344'
  // 从search或hash中获取商品编号 1440004
  let id = ''
  if (search.includes('type=wbl&id='))
    id = search.split('type=wbl&id=')[1]
  else if (hash.includes('#/detail/wbl/'))
    id = hash.split('#/detail/wbl/')[1]

  const res = await getJx3ShAccountIDApi(id)
  const consignment_id = res?.data?.detail?.wanbaolou_id || ''
  if (consignment_id) {
    const token = getLocalStorage('token', '')
    const res = await addFollowApiByToken(consignment_id, 2, token)
    if (res.code === 1) {
      successNotify('关注成功')
    }
    else if (res.msg === '抱歉，请先登录') {
      errNotify('关注失败，请先登录万宝楼')
      setTimeout(() => {
        window.open('https://jx3.seasunwbl.com', '_blank')
      }, 2000)
    }
    else {
      errNotify(res.msg)
    }
  }
  else {
    errNotify('获取商品编号失败')
  }
}
