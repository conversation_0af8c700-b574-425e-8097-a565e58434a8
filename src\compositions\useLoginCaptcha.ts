import { ref } from 'vue'
import { warningNotify } from '~/compositions/useNotify'
import type { GeetestObj } from '~/type'
import { getSessionStorage, handlingErrors, setSessionStorage } from '~/utils/utils'
import initGeetest4 from '~/utils/gt'
import { loginCaptchaId, orderCaptchaTime } from '~/compositions/useSetting'

const loginCaptchaLoading = ref(false)
const loginCaptchaState = ref(false)
const loginGeetestObj = ref<GeetestObj>(null)
const lastLoginCaptchaTime = ref(0)
const loginCaptchaText = ref('请先完成验证码验证')
let timer: any = null

function getLocalCaptcha() {
  const localCaptcha = getSessionStorage('loginCaptcha', { geetest: null, captchaExpireTime: 0, lastLoginCaptchaTime: 0 })
  if (localCaptcha.geetest && localCaptcha.captchaExpireTime > new Date().getTime()) {
    loginGeetestObj.value = localCaptcha.geetest
    loginCaptchaState.value = true
    lastLoginCaptchaTime.value = localCaptcha.lastLoginCaptchaTime
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
    // 倒计时 10 min，以s为单位，每秒更改文本，初始文本为，已完成验证，有效期 600 秒
    let count = Math.floor((localCaptcha.captchaExpireTime - new Date().getTime()) / 1e3)
    function startCountdown() {
      if (count <= 0) {
        initLoginCaptchaValue()
        return
      }
      loginCaptchaText.value = `已完成验证，验证码有效时间： ${count} 秒`
      count--
      timer = setTimeout(startCountdown, 1000)
    }
    startCountdown()
  }
}

function setLocalCaptcha(geetest: GeetestObj) {
  const captchaExpireTime = new Date().getTime() + orderCaptchaTime * 60 * 1e3
  setSessionStorage('loginCaptcha', { geetest, captchaExpireTime, lastLoginCaptchaTime: Date.now() })
}

function removeLocalCaptcha() {
  setSessionStorage('loginCaptcha', { geetest: null, captchaExpireTime: 0, lastLoginCaptchaTime: 0 })
}

/**
 * 初始化验证码值
 */
function initLoginCaptchaValue() {
  loginCaptchaState.value = false
  loginGeetestObj.value = null
  loginCaptchaText.value = '请先完成验证码验证'
  removeLocalCaptcha()
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
}
/**
 * 验证码初始化
 * @param captchaObj 验证码对象
 */
const handlerEmbed = function (captchaObj: any) {
  captchaObj.onReady(() => {
    loginCaptchaLoading.value = false
  }).onSuccess(() => {
    lastLoginCaptchaTime.value = new Date().getTime()
    loginGeetestObj.value = captchaObj.getValidate()
    setLocalCaptcha(loginGeetestObj.value)
    loginCaptchaState.value = true
    if (timer)
      clearTimeout(timer)
    // 倒计时 10 min，以s为单位，每秒更改文本，初始文本为，已完成验证，有效期 600 秒
    let count = orderCaptchaTime * 60
    function startCountdown() {
      if (count <= 0) {
        initLoginCaptchaValue()
        return
      }
      loginCaptchaText.value = `已完成验证，验证码有效时间： ${count} 秒`
      count--
      timer = setTimeout(startCountdown, 1000)
    }
    startCountdown()
  }).onError((e: any) => {
    loginCaptchaLoading.value = false
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox() // 显示验证码
}
let getCaptchaTimer: any = null
function initTimer() {
  if (getCaptchaTimer) {
    clearTimeout(getCaptchaTimer)
    getCaptchaTimer = null
  }
}
function loadLoginCaptcha() {
  if (loginCaptchaLoading.value)
    return
  loginCaptchaLoading.value = true
  initLoginCaptchaValue()
  initTimer()
  initGeetest4({ product: 'bind', captchaId: loginCaptchaId }, handlerEmbed)
  loginCaptchaLoading.value = false
  initTimer()

  getCaptchaTimer = setTimeout(() => {
    if (loginCaptchaLoading.value === true) {
      warningNotify('验证码初始化超时，请重试。')
      loginCaptchaLoading.value = false
    }
  }, 6 * 1e3)
}

export function useLoginCaptcha() {
  getLocalCaptcha()
  return {
    loadLoginCaptcha,
    loginCaptchaLoading,
    loginCaptchaState,
    loginGeetestObj,
    initLoginCaptchaValue,
    loginCaptchaText,
    lastLoginCaptchaTime,
  }
}
