// 备忘，主要功能
// 1. 自动检查是否维护完成
// 2. 维护完成后自动登录、获取订单信息、自动下单，账号密码以及物品ID需要提前输入
// 3. 需要登录验证码、下单验证码，两个验证码对象，需要提前输入
// 4. 如果订单处于公示期且结束时间大于 5 分钟，自动取消订单

import { ref } from 'vue'
import { checkServerIsOpenApi, getDetailInfoApi, loginApi } from '~/api/wbl'
import { errNotify, successNotify } from '~/compositions/useNotify'
import { isMaintenanceRef, openServiceRepeatDelayTime } from '~/compositions/useSetting'
import { encryptPwd, getSessionStorage, setSessionStorage } from '~/utils/utils'
import { useLoginCaptcha } from '~/compositions/useLoginCaptcha'
import { openZfbDialogByMaintenance } from '~/compositions/useZfb'
import { useCaptcha } from '~/compositions/useCaptcha'

// 先输入好账号密码、物品ID，等待维护完成后自动登录、获取订单信息、自动下单
// 需要登录验证码、下单验证码，两个验证码对象，需要提前输入
// 如果订单处于公示期且结束时间大于验证码失效时间，直接跳转主页
// 如果订单处于公示期且结束时间小于验证码失效时间，自动登录、获取订单信息、自动下单
const account = ref('')
const password = ref('')
const itemId = ref('')
const isShowMaintenanceDialog = ref(false)

export function openMaintenanceDialog() {
  isShowMaintenanceDialog.value = true
}
export function isMaintenance() {
  if (isMaintenanceRef.value)
    return true
  // 获取location中的search参数，如果有type=maintenance则返回true
  const search = window.location.search
  return search.includes('type=maintenance')
}

function isMobile() {
  return window.location.host === 'm.seasunwbl.com'
}

function isCreateOrder() {
  // account、password、itemId、loginCaptchaState、loginGeetestObj
  const { loginCaptchaState, loginGeetestObj } = useLoginCaptcha()
  const { captchaState, geetestObj } = useCaptcha()
  return account.value && password.value && itemId.value && loginCaptchaState.value && loginGeetestObj.value && captchaState.value && geetestObj.value.length
}

function initData() {
  setSessionStorage('maintenance', {
    account: account.value,
    password: '',
    itemId: '',
  })
}

async function autoCreateOrder() {
  // 自动登录
  const { loginCaptchaState, loginGeetestObj, initLoginCaptchaValue } = useLoginCaptcha()
  if (loginCaptchaState.value) {
    const res = await loginApi(account.value, encryptPwd(password.value), loginGeetestObj.value, 'include')
    initLoginCaptchaValue()
    if (res.code === 1) {
      successNotify('登录成功')
      const detailRes = await getDetailInfoApi(itemId.value, 2)
      initData()
      if (res?.code === 1)
        await openZfbDialogByMaintenance(itemId.value, isMobile())
      else
        throw new Error(detailRes.msg)
    }
    else {
      errNotify(res.msg)
      throw new Error(res.msg)
    }
  }
}

function toHomePage() {
  if (isMobile()) {
    window.location.href = 'https://m.seasunwbl.com/jx3/login.html'
  }
  else {
    successNotify('已开楼')
    if ('Notification' in window && Notification.permission === 'granted') {
      // eslint-disable-next-line no-new
      new Notification('万宝楼维护结束', {
        body: '万宝楼维护结束啦！！冲冲冲！！！',
        icon: 'https://jx3.seasunwbl.com/favicon.ico',
      })
    }
    window.location.href = 'https://jx3.seasunwbl.com'
  }
}

/**
 * 检查是否维护完成
 */
export async function checkMaintenance() {
  // 如果不是维护页面，直接返回
  if (!isMaintenance())
    return
  async function check() {
    const res = await checkServerIsOpenApi()
    if (res.msg?.includes('维护中')) {
      errNotify(`蹲开楼：${res.msg}`)
      setTimeout(async () => {
        await check()
      }, openServiceRepeatDelayTime.value)
    }
    else {
      isMaintenanceRef.value = false
      if (isCreateOrder()) {
        try {
          await autoCreateOrder()
        }
        catch (e) {
          toHomePage()
        }
      }
      else {
        toHomePage()
      }
    }
  }
  await check()
}

export function useMaintenance() {
  function onSave() {
    isShowMaintenanceDialog.value = false
    setSessionStorage('maintenance', {
      account: account.value,
      password: password.value,
      itemId: itemId.value,
    })
    successNotify('保存成功')
  }
  function init() {
    const maintenance = getSessionStorage('maintenance', { account: '', password: '', itemId: '' })
    account.value = maintenance.account
    password.value = maintenance.password
    itemId.value = maintenance.itemId
  }
  init()
  return {
    account,
    password,
    itemId,
    isShowMaintenanceDialog,
    onSave,
  }
}
