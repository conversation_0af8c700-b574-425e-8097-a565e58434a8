import { ref } from 'vue'
import QRCode from 'qrcode'
import {
  encryptPwd,
  getLocalStorage,
  getSessionStorage,
  handlingErrors,

  setLocalStorage,
  setSessionStorage,
} from '~/utils/utils'
import { createOrderApi, getAdditionalServiceApi, loginApi, payApi, setTokenApi } from '~/api/wbl'
import { cookieIsolationManager } from '~/utils/CookieIsolationManager'
import { shouldAutoRefreshOnFailure } from '~/utils/CookieIsolationConfig'
import initGeetest4 from '~/utils/gt'
import { errNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import type {
  AdditionalService,
  AdditionalServiceOptions,
  DeliveryInfo,
  GeetestObj,
  GoodsDetail,
  OrderCallback,
  PayCallback,
} from '~/type'
import { openSelectRoleDialog } from '~/compositions/useSelectRole'
import {
  loginCaptchaId,
  orderCaptchaId,
  orderCaptchaTime,

  orderRetryTimes,
  payRetryDelayTime,
  payRetryTimes,
} from '~/compositions/useSetting'
import { useConcurrencyControl } from '~/compositions/useConcurrencyControl'

// 当前选择框
export const currSelect = ref('')
// 是否是角色多开
export const isRoleMultiOpen = ref(false)
// 是否是外观多开
export const isSkinMultiOpen = ref(false)
export const isShowLoginDialog = ref(false)
export const currQrcode = ref('')
const account = ref('')
const pwd = ref('')
export function useLoginDialog() {
  return {
    account,
    pwd,
  }
}

export const zfbUrls = ref<string[]>([])

function getLocalAccountTokens() {
  const list = getSessionStorage('accountTokenList', []) as AccountToken[]

  // 修复token过期检查逻辑，添加更宽松的过期时间检查
  const currentTime = Date.now()
  const filterList = list.filter((item) => {
    // 如果没有过期时间，认为是有效的（向后兼容）
    if (!item.expire)
      return true

    // 添加5分钟的缓冲时间，避免因为时间差导致的误判
    const bufferTime = 5 * 60 * 1000 // 5分钟缓冲
    const isValid = item.expire > (currentTime - bufferTime)

    if (!isValid)
      console.log(`账号 ${item.account} 的token已过期，过期时间: ${new Date(item.expire).toLocaleString()}, 当前时间: ${new Date(currentTime).toLocaleString()}`)

    return isValid
  })

  if (filterList.length !== list.length) {
    console.log(`过滤掉 ${list.length - filterList.length} 个过期的账号token`)
    setSessionStorage('accountTokenList', filterList)
  }

  if (zfbUrls.value.length !== filterList.length)
    zfbUrls.value = Array.from({ length: filterList.length }).fill('') as string[]
  return filterList
}
// 多开验证码对象
export const multiGeetestObj = ref< GeetestObj[] >([])
interface LocalGeetestObj {
  geetestObj: GeetestObj
  expire: number
}
function getLocalGeetest() {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  const filterList = list.filter(item => item.expire > Date.now())
  if (filterList.length !== list.length)
    setSessionStorage('multiGeetestObj', filterList)
  multiGeetestObj.value = filterList.map(item => item.geetestObj)
}
getLocalGeetest()

function setLocalGeetest(geetestObj: GeetestObj) {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  list.push({ geetestObj, expire: Date.now() + 60 * orderCaptchaTime * 1000 })
  setSessionStorage('multiGeetestObj', list)
}

function removeLocalGeetestByCaptchaId(captchaId: string) {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  const filterList = list.filter(item => item?.geetestObj?.captcha_id !== captchaId)
  setSessionStorage('multiGeetestObj', filterList)
}

function getGeetestObj() {
  if (multiGeetestObj.value.length === 0) {
    errNotify(`验证码数量不足。`)
  }
  else {
    const geetestObj = multiGeetestObj.value.pop() as GeetestObj
    if (geetestObj) {
      removeLocalGeetestByCaptchaId(geetestObj.captcha_id)
      return geetestObj
    }
  }
  return null
}

const currLoginInfo = ref()
interface AccountToken {
  account: string
  token: string
  expire: number
  separationServiceFee?: number
  isSelectedRole?: boolean
  roleInfo?: string
}
// 多开账号token列表
export const accountTokenList = ref<AccountToken[]>(getLocalAccountTokens())
// 是否打开登录框
export const isOpenLoginDialog = ref(false)

export async function selectRole(account: AccountToken) {
  if (!account?.isSelectedRole)
    openSelectRoleDialog(account.token)
  else
    successNotify('该账号已选择角色')
}

export const currAccount = ref(`账号：${getSessionStorage('account', '首次登录账号')}`)

/**
 * 检查token是否有效（未过期）
 */
export function isTokenValid(token: string): boolean {
  const account = accountTokenList.value.find(item => item.token === token)
  if (!account)
    return false

  // 如果没有过期时间，认为是有效的（向后兼容）
  if (!account.expire)
    return true

  // 添加5分钟的缓冲时间
  const bufferTime = 5 * 60 * 1000 // 5分钟缓冲
  const isValid = account.expire > (Date.now() - bufferTime)

  if (!isValid)
    console.log(`账号 ${account.account} 的token已过期，过期时间: ${new Date(account.expire).toLocaleString()}`)

  return isValid
}

/**
 * 刷新账号token的过期时间
 */
export function refreshTokenExpire(token: string, newExpireTime?: number): boolean {
  const index = accountTokenList.value.findIndex(item => item.token === token)
  if (index === -1)
    return false

  const expireTime = newExpireTime || (Date.now() + 3600 * 1000) // 默认1小时
  accountTokenList.value[index].expire = expireTime
  setSessionStorage('accountTokenList', accountTokenList.value)

  console.log(`刷新账号 ${accountTokenList.value[index].account} 的token过期时间: ${new Date(expireTime).toLocaleString()}`)
  return true
}

/**
 * 初始化token状态，在页面加载时调用
 * 确保所有token状态正确，避免页面刷新后token丢失
 */
export function initializeTokenStates() {
  try {
    console.log('初始化token状态...')

    // 重新加载账号列表，应用过期检查
    const validAccounts = getLocalAccountTokens()
    accountTokenList.value = validAccounts

    // 检查当前token是否有效
    const currentToken = getSessionStorage('currToken')
    const mainToken = getLocalStorage('token')

    if (currentToken && currentToken !== mainToken) {
      // 如果当前token是多开账号的token，检查其有效性
      if (!isTokenValid(currentToken)) {
        console.log('当前token已过期，切换回主账号')
        setSessionStorage('currToken', mainToken)

        // 更新当前账号显示
        const mainAccount = getSessionStorage('account', '主账号')
        currAccount.value = `账号：${mainAccount}`

        warningNotify('当前账号登录状态已过期，已切换回主账号')
      }
      else {
        // 刷新有效token的过期时间
        refreshTokenExpire(currentToken)
      }
    }

    console.log(`token状态初始化完成，有效账号数量: ${validAccounts.length}`)
  }
  catch (error) {
    console.error('初始化token状态失败:', error)
  }
}

/**
 * 处理添加账号后的页面刷新
 * 简化方案：直接刷新页面解决Cookie污染问题
 */
async function handleAccountAddRefresh() {
  try {
    // 检查是否应该自动刷新
    const autoRefresh = shouldAutoRefreshOnFailure('ADD_ACCOUNT')

    if (autoRefresh) {
      console.log('添加新账号完成，准备刷新页面解决Cookie污染问题')

      // 给用户看到成功提示的时间
      const refreshDelay = 1500 // 1.5秒延迟

      // 提示用户即将刷新
      warningNotify('账号添加成功，即将刷新页面以确保状态同步')

      // 延迟刷新，让用户看到成功提示
      setTimeout(() => {
        console.log('执行页面刷新以解决Cookie污染问题')
        window.location.reload()
      }, refreshDelay)
    }
    else {
      // 如果配置为不自动刷新，则只提示用户
      warningNotify('账号添加成功，建议刷新页面以确保状态同步')
    }
  }
  catch (error) {
    console.error('处理账号添加刷新失败:', error)
    // 如果出错，直接刷新页面作为兜底
    warningNotify('账号添加完成，即将刷新页面')
    setTimeout(() => window.location.reload(), 1000)
  }
}

export function setToken(token: string, account?: string) {
  // 如果token在列表中已存在， 则不设置
  if (token === '')
    return
  const index = accountTokenList.value.findIndex(item => item.token === token)
  setSessionStorage('currToken', token)
  if (index === -1) {
    setLocalStorage('token', token)
    if (account) {
      setSessionStorage('account', account)
      currAccount.value = `账号：${account}`
    }
  }
}

async function login(captcha: GeetestObj) {
  try {
    // 使用标准登录API
    const res = await loginApi(currLoginInfo.value.account, encryptPwd(currLoginInfo.value.password), captcha)
    if (res.code === 1) {
      account.value = ''
      pwd.value = ''
      isShowLoginDialog.value = false
      // 先检查是否已经登录过， 如果已经登录过， 则替换token及过期时间
      const index = accountTokenList.value.findIndex(item => item.account === currLoginInfo.value.account)
      // 设置过期时间：1小时
      const expireTime = Date.now() + 3600 * 1000 // 1小时

      if (index !== -1) {
        accountTokenList.value[index].token = res.data.ts_session_id
        accountTokenList.value[index].expire = expireTime
        zfbUrls.value[index] = ''
        console.log(`更新账号 ${currLoginInfo.value.account} 的token，过期时间: ${new Date(expireTime).toLocaleString()}`)
      }
      else {
        accountTokenList.value.push({
          account: currLoginInfo.value.account,
          token: res.data.ts_session_id,
          expire: expireTime,
        })
        zfbUrls.value.push('')
        console.log(`添加新账号 ${currLoginInfo.value.account}，过期时间: ${new Date(expireTime).toLocaleString()}`)
      }
      isRoleMultiOpen.value = false
      isSkinMultiOpen.value = false
      setSessionStorage('accountTokenList', accountTokenList.value)
      successNotify(`账号 [ ${currLoginInfo.value.account} ] 登录成功`)

      // 简化方案：添加新账号后直接刷新页面解决Cookie污染问题
      await handleAccountAddRefresh()
    }
    else {
      errNotify(res.msg)
    }
  }
  catch (error) {
    console.error('账号登录过程中出现错误:', error)
    errNotify('账号登录失败，请重试')
  }
}

function loginEmbed(captchaObj: any) {
  captchaObj.onReady(() => {

  }).onSuccess(async () => {
    const captcha = captchaObj.getValidate() as GeetestObj
    await login(captcha)
  }).onError((e: any) => {
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox()
}
export const loadAccountCallBack = ref<Function | null>(null)

function handlerEmbedSuccess(geetestTemp: GeetestObj) {
  multiGeetestObj.value.push(geetestTemp)
  setLocalGeetest(geetestTemp)
  if (multiGeetestObj.value.length < accountTokenList.value.length + 1) {
    loadAccountCaptcha()
  }
  else {
    if (loadAccountCallBack.value) {
      loadAccountCallBack.value()
      loadAccountCallBack.value = null
    }
    if (currSelect.value === 'role')
      isRoleMultiOpen.value = true
    else if (currSelect.value === 'skin')
      isSkinMultiOpen.value = true
    currSelect.value = ''
  }
}
function handlerEmbed(captchaObj: any) {
  captchaObj.onReady(() => {

  }).onSuccess(() => {
    const geetestTemp = captchaObj.getValidate() as GeetestObj
    handlerEmbedSuccess(geetestTemp)
  }).onError((e: any) => {
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox()
}

export async function loadAccountCaptcha() {
  initGeetest4({ product: 'bind', captchaId: orderCaptchaId }, handlerEmbed)
  await new Promise(resolve => setTimeout(resolve, 1000))
}

export function loginWbl(account: string, password: string) {
  currLoginInfo.value = { account, password }
  if (account && password)
    initGeetest4({ product: 'bind', captchaId: loginCaptchaId }, loginEmbed)
  else
    warningNotify('请输入账号密码')
}

function showQrcode(str: string, index: number) {
  const opts = {
    errorCorrectionLevel: 'Q',
    quality: 1,
    margin: 1,
    width: 300,
    color: {
      dark: '#1688ff',
      light: '#fff',
    },
  }
  // 将二维码渲染到 canvas 中，然后将 canvas 添加到目标元素
  QRCode.toDataURL(str, opts, (err: any, url: string) => {
    if (err) {
      errNotify('生成二维码失败')
      return
    }
    if (index === -1)
      currQrcode.value = url
    else
      zfbUrls.value[index] = url
  })
}

async function pay(orderId: string, type: number, token: string, index: number) {
  let retryCount = 0
  let res
  let errorOccurred = false
  let lastError: any = null

  do {
    try {
      if (retryCount > 0)
        await new Promise(resolve => setTimeout(resolve, payRetryDelayTime))

      res = await payApi(orderId, type, token)
      errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
      lastError = null
    }
    catch (error) {
      // 如果捕获到错误，设置 errorOccurred 为 true
      errorOccurred = true
      lastError = error
      console.error(`支付API调用失败 (重试 ${retryCount}/${payRetryTimes.value}):`, error)
    }
    retryCount++
  } while ((errorOccurred || res?.code !== 1) && retryCount <= payRetryTimes.value)

  if (res?.code !== 1) {
    const accountLabel = index === -1 ? '主账号' : `账号${index + 1}`
    if (errorOccurred && lastError) {
      console.error(`${accountLabel}支付请求失败:`, lastError)
      errNotify(`${accountLabel}支付请求失败，请检查网络连接`)
    }
    else {
      errNotify(`${accountLabel}支付失败：${res?.msg || '未知错误'}`)
    }
  }
  else {
    const pay_attach = (res.data as PayCallback).pay_attach
    showQrcode(pay_attach, index)

    // 添加成功日志
    const accountLabel = index === -1 ? '主账号' : `账号${index + 1}`
    console.log(`${accountLabel}支付二维码生成成功`)
  }
}

export async function multiAdditionalService(orderId: string) {
  // 网页登录用户的分离费
  const originToken = getLocalStorage('token')
  const originRes = await getAdditionalServiceApi(orderId, originToken)
  const originData = originRes.data.list as AdditionalService[]
  const originSeparationServiceFee = originData.find(item => item.name === 'separation_service')?.value
  setSessionStorage('originSeparationServiceFee', originSeparationServiceFee)
  // 插件登录用户的分离费
  for (const item of accountTokenList.value) {
    const index = accountTokenList.value.indexOf(item)
    if (item.separationServiceFee !== undefined)
      continue
    const res = await getAdditionalServiceApi(orderId, item.token)
    if (res.code === 1) {
      const data = res.data.list as AdditionalService[]
      accountTokenList.value[index].separationServiceFee = data.find(item => item.name === 'separation_service')?.value
    }
    else {
      errNotify(`账号 [ ${item.account} ] 获取角色分离费失败：${res.msg}`)
    }
  }
  setSessionStorage('accountTokenList', accountTokenList.value)
}

function getAdditionalInfoByToken(additionalServiceSum: number, additionalService: AdditionalService[] | undefined, currAccount?: AccountToken) {
  let tempAdditionalServiceSum = JSON.parse(JSON.stringify(additionalServiceSum)) as number
  const tempAdditionalService = JSON.parse(JSON.stringify(additionalService)) as AdditionalService[]
  const tempSepFee = additionalService?.find(item => item.name === 'separation_service')?.value || 0
  if (!currAccount) {
    const originFee = getSessionStorage('originSeparationServiceFee')
    if (tempSepFee !== originFee) {
      tempAdditionalServiceSum = tempAdditionalServiceSum - tempSepFee + originFee
      tempAdditionalService.find(item => item.name === 'separation_service')!.value = originFee
    }
  }
  else if (currAccount.separationServiceFee !== undefined && tempAdditionalService !== undefined) {
    if (tempSepFee !== currAccount.separationServiceFee) {
      tempAdditionalServiceSum = tempAdditionalServiceSum - tempSepFee + currAccount.separationServiceFee
      tempAdditionalService.find(item => item.name === 'separation_service')!.value = currAccount.separationServiceFee
    }
  }
  return { tempAdditionalServiceSum, tempAdditionalService }
}

const { executeWithConcurrencyControl, executeBatch } = useConcurrencyControl()

// 使用并发控制的订单创建函数
async function createOrderAndNotifyWithConcurrency(type: number, row: GoodsDetail, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, currAccount: AccountToken, i: number, deliveryInfo?: DeliveryInfo) {
  return await executeWithConcurrencyControl(
    async () => {
      const geetestObj = getGeetestObj() as GeetestObj
      if (!geetestObj)
        throw new Error('验证码数量不足，创建订单失败。')

      const res = await createOrderApi(row, geetestObj, type, additionalServiceSum, transferServiceObj, additionalService, currAccount.token, deliveryInfo)

      if (res.code === 1) {
        successNotify(`账号 [ ${currAccount.account} ] 订单创建成功`)
        const orderId = (res.data as OrderCallback).order_id
        await pay(orderId, type, currAccount.token, i)
        return res
      }
      else {
        throw new Error(`账号 [ ${currAccount.account} ] 订单创建失败：${res.msg}`)
      }
    },
    {
      priority: 2,
      maxRetries: orderRetryTimes.value,
      id: `account_${currAccount.account}_order_${i}`,
    },
  )
}

export async function multiCreateOrder(row: GoodsDetail, type: number, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, deliveryInfo?: DeliveryInfo) {
  const geetestObj = getGeetestObj() as GeetestObj
  if (!geetestObj) {
    errNotify('验证码数量不足，创建订单失败。')
    return
  }

  // 清理之前的二维码状态
  console.log('多开抢号开始，清理二维码状态')
  currQrcode.value = ''
  zfbUrls.value = zfbUrls.value.map(() => '')

  // 多开抢外观时，强制设置收获方式为万宝楼
  let finalDeliveryInfo = deliveryInfo
  if (type === 3) {
    finalDeliveryInfo = {
      delivery_destination: 2, // 强制设置为万宝楼收货
    }
    console.log('多开抢外观模式：已强制设置收获方式为万宝楼')
  }

  // 启动Cookie隔离模式，防止多开抢号过程中Cookie冲突
  console.log('开始多开抢号，启动Cookie隔离模式')
  cookieIsolationManager.startIsolation()

  const token = getLocalStorage('token')

  // 使用并发控制执行主账号订单创建
  try {
    const mainOrderResult = await executeWithConcurrencyControl(
      async () => {
        if (type === 2) {
          const { tempAdditionalServiceSum, tempAdditionalService } = getAdditionalInfoByToken(additionalServiceSum, additionalService)
          return await createOrderApi(row, geetestObj, type, tempAdditionalServiceSum, transferServiceObj, tempAdditionalService, token)
        }
        else {
          return await createOrderApi(row, geetestObj, type, additionalServiceSum, transferServiceObj, additionalService, token, finalDeliveryInfo)
        }
      },
      { priority: 3, maxRetries: orderRetryTimes.value, id: 'main_account_order' },
    )

    if (mainOrderResult?.code === 1) {
      successNotify(`当前账号订单创建成功`)
      const orderId = (mainOrderResult.data as OrderCallback).order_id

      // 分离支付逻辑的错误处理，避免支付失败被误认为订单创建失败
      try {
        await pay(orderId, type, token, -1)
      }
      catch (payError) {
        console.error('主账号支付请求失败:', payError)
        errNotify(`主账号支付请求失败，但订单已创建成功（订单号：${orderId}）`)
      }
    }
    else {
      errNotify(`当前账号订单创建失败：${mainOrderResult.msg}`)
    }
  }
  catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    errNotify(`当前账号订单创建失败：${errorMessage}`)
  }

  // 为多开账号创建订单请求数组
  const multiAccountRequests = accountTokenList.value.map((currAccount, i) => {
    return async () => {
      if (type === 2) {
        const { tempAdditionalServiceSum, tempAdditionalService } = getAdditionalInfoByToken(additionalServiceSum, additionalService, currAccount)
        return await createOrderAndNotifyWithConcurrency(type, row, tempAdditionalServiceSum, transferServiceObj, tempAdditionalService, currAccount, i)
      }
      else if (type === 3) {
        return await createOrderAndNotifyWithConcurrency(type, row, additionalServiceSum, transferServiceObj, additionalService, currAccount, i, finalDeliveryInfo)
      }
    }
  })

  // 使用批量执行，智能控制并发
  try {
    await executeBatch(multiAccountRequests, {
      batchSize: 2, // 每批2个请求
      priority: 2,
      maxRetries: orderRetryTimes.value,
    })
    console.log('多开账号订单创建批量处理完成')
  }
  catch (error) {
    console.error('多开账号批量订单创建失败:', error)
    errNotify('部分多开账号订单创建失败，请查看详细日志')
  }
  finally {
    // 无论成功失败都要结束Cookie隔离模式和清理状态
    console.log('多开抢号完成，开始清理状态')

    try {
      // 结束Cookie隔离模式
      cookieIsolationManager.endIsolation()
      console.log('Cookie隔离模式已结束')
    }
    catch (error) {
      console.error('结束Cookie隔离模式失败:', error)
    }

    try {
      // 恢复主账号token状态
      await setTokenApi()
      console.log('主账号token状态已恢复')
    }
    catch (error) {
      console.error('恢复主账号token状态失败:', error)
    }

    // 显示完成提示
    const totalAccounts = accountTokenList.value.length + 1 // +1 for main account
    const itemType = type === 3 ? '外观' : '角色'
    successNotify(`多开抢${itemType}流程已完成，共处理 ${totalAccounts} 个账号`)
  }
}
