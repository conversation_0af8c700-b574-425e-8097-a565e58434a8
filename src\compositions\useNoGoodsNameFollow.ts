import { computed, ref } from 'vue'
import { getLocalStorage, setLocalStorage } from '~/utils/utils'
import type { GoodsDetail } from '~/type'

import { getGoodsListApi } from '~/api/wbl'
import { GoodsState } from '~/api/enums'
import { errNotify } from '~/compositions/useNotify'
import {
  followGoods,
  getCurrentFollowGoodsNum,
  hideAutoFollowLoading,
  isStopFollow,
  showAutoFollowLoading,
} from '~/compositions/useConcern'
import { get30AvgPrice, getMinAvgPrice, getNowAvgPrice } from '~/compositions/useGoodsInfo'

const priceDiscount = ref(9)
// 最大公示时间
const maxPublicTime = ref(12)
const minPublicTime = ref(0)
// 不关注的商品
const noFollowGoods = ref<string[]>([])
// 不关注的商品类型
const noFollowGoodsType = ref<string[]>([])
const minFollowNum = ref(10)

export const currPriceMode = ref('minAvg')
export const priceModeOptions = ref([
  { label: '30 日最低均价', value: 'minAvg' },
  { label: '30 日均价', value: '30Avg' },
  { label: '当天均价', value: 'nowAvg' },
])
export const currPriceLabel = computed(() => {
  return priceModeOptions.value.find(item => item.value === currPriceMode.value)?.label
})

function init() {
  const noGoodsNameFollow = getLocalStorage('noGoodsNameFollow', {})
  priceDiscount.value = noGoodsNameFollow.priceDiscount ?? 9
  maxPublicTime.value = noGoodsNameFollow.maxPublicTime ?? 12
  minPublicTime.value = noGoodsNameFollow.minPublicTime ?? 0
  noFollowGoods.value = noGoodsNameFollow.noFollowGoods ?? []
  noFollowGoodsType.value = noGoodsNameFollow.noFollowGoodsType ?? []
  currPriceMode.value = noGoodsNameFollow.currPriceMode ?? 'minAvg'
  minFollowNum.value = noGoodsNameFollow.minFollowNum ?? 10
}

export function setNoGoodsNameFollow() {
  if (minPublicTime.value > maxPublicTime.value) {
    const noGoodsNameFollow = getLocalStorage('noGoodsNameFollow', {})
    maxPublicTime.value = noGoodsNameFollow.maxPublicTime ?? 12
    minPublicTime.value = noGoodsNameFollow.minPublicTime ?? 0
    // 持久化存储不指定商品名称关注的数据
    setLocalStorage('noGoodsNameFollow', {
      priceDiscount: priceDiscount.value ?? 9,
      maxPublicTime: maxPublicTime.value ?? 12,
      minPublicTime: minPublicTime.value ?? 0,
      noFollowGoods: noFollowGoods.value ?? [],
      noFollowGoodsType: noFollowGoodsType.value ?? [],
      currPriceMode: currPriceMode.value ?? 'minAvg',
      minFollowNum: minFollowNum.value ?? 10,
    })
  }
  else {
    // 持久化存储不指定商品名称关注的数据
    setLocalStorage('noGoodsNameFollow', {
      priceDiscount: priceDiscount.value ?? 9,
      maxPublicTime: maxPublicTime.value ?? 12,
      minPublicTime: minPublicTime.value ?? 0,
      noFollowGoods: noFollowGoods.value ?? [],
      noFollowGoodsType: noFollowGoodsType.value ?? [],
      currPriceMode: currPriceMode.value ?? 'minAvg',
      minFollowNum: minFollowNum.value ?? 10,
    })
  }
}

/**
 * 是否关注商品
 * @param goods 商品信息
 * @param price 预期商品价格
 */
async function isFollowGoods(goods: GoodsDetail, price: number) {
  return (
    !goods.is_followed
    && goods.single_unit_price <= price * priceDiscount.value / 10
    && ((goods.remaining_time <= maxPublicTime.value * 60 * 60 && goods.remaining_time >= minPublicTime.value * 60 * 60) || goods.state === 5)
    && !noFollowGoods.value.includes(goods.info)
    && !noFollowGoodsType.value.includes(goods.attrs?.appearance_type_name || '')
  )
}

async function getGoodsPriceByMode(goods: GoodsDetail) {
  switch (currPriceMode.value) {
    case 'minAvg':
      return await getMinAvgPrice(goods.info, goods.consignment_id)
    case 'nowAvg':
      return await getNowAvgPrice(goods.info, goods.consignment_id)
    case '30Avg':
      return await get30AvgPrice(goods.info, goods.consignment_id)
    default:
      return getMinAvgPrice(goods.info, goods.consignment_id)
  }
}

/**
 * 自动关注不指定商品名称的商品
 */
export async function autoFollowNoGoodsName() {
  if (isStopFollow.value) {
    errNotify('正在自动关注，请勿重复操作')
    return
  }
  showAutoFollowLoading()
  let page = 1
  let currentFollowGoodsNum = await getCurrentFollowGoodsNum()
  // 缓存一个价格，避免重复请求，key 为商品名称, value 为价格
  const priceMap = new Map<string, number>()
  // 循环获取商品列表
  try {
    while (isStopFollow.value) {
      // 如果关注的商品数量超过10，则停止关注
      if (currentFollowGoodsNum >= 20) {
        errNotify(`已经达到最大关注数 20 个，已停止关注`)
        hideAutoFollowLoading()
        break
      }
      const res = await getGoodsListApi(GoodsState.All, '', page)
      if (res?.data?.list?.length === 0) {
        hideAutoFollowLoading()
        break
      }
      const goodsList = res.data.list as GoodsDetail[]
      for (const goods of goodsList) {
        const price = priceMap.get(goods.info) ?? await getGoodsPriceByMode(goods)
        priceMap.set(goods.info, price)
        if (await isFollowGoods(goods, price)) {
          const isFollow = await followGoods(goods)
          if (isFollow) {
            currentFollowGoodsNum++
            if (currentFollowGoodsNum >= 20) {
              errNotify(`已经达到最大关注数 20 个，停止关注`)
              hideAutoFollowLoading()
              return
            }
          }
          else {
            hideAutoFollowLoading()
          }
        }
        if (goods.followed_num < minFollowNum.value) {
          hideAutoFollowLoading()
          return
        }
      }

      if (page % 4 === 0)
        await new Promise(resolve => setTimeout(resolve, 2000))

      page++
    }
  }
  catch (e) {
    hideAutoFollowLoading()
  }
}

export function useNoGoodsNameFollow() {
  init()
  return { priceDiscount, maxPublicTime, minPublicTime, noFollowGoods, noFollowGoodsType, minFollowNum }
}
