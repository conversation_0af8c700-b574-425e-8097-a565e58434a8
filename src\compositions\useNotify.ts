import { Notify } from 'quasar'

function isReload(msg: string) {
  if (msg === '抱歉，请先登录' || msg === '抱歉，操作频繁，请验证后再试') {
    setTimeout(() => {
      if (location.host === 'm.seasunwbl.com' && msg === '抱歉，请先登录')
        window.location.href = 'https://m.seasunwbl.com/jx3/login.html'
      else
        window.location.reload()
    }, 1000)
  }
}

export function successNotify(msg: string) {
  Notify.create({
    color: 'green',
    message: msg,
  })
  isReload(msg)
}

export function errNotify(msg: string) {
  Notify.create({
    color: 'red',
    message: msg,
  })
  isReload(msg)
}

export function warningNotify(msg: string) {
  Notify.create({
    color: 'orange',
    message: msg,
  })
  isReload(msg)
}

export function infoNotify(msg: string) {
  Notify.create({
    color: 'teal',
    message: msg,
  })
  isReload(msg)
}
