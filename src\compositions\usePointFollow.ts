import { ref } from 'vue'
import { getLocalStorage, setLocalStorage } from '~/utils/utils'
import type { GoodsDetail } from '~/type'

import { getAppearanceBaseInfoApi, getFollowByPointListApi } from '~/api/wbl'
import { errNotify } from '~/compositions/useNotify'
import {
  followGoods,
  getCurrentFollowGoodsNum,
  hideAutoFollowLoading,
  isStopFollow,
  showAutoFollowLoading,
} from '~/compositions/useConcern'

// 最大公示时间
const publicTime = ref({
  min: 0,
  max: 12,
})
// 不关注的商品
const noFollowGoods = ref<string[]>([])
// 关注的商品类型
const followGoodsType = ref<number[]>([])
const minPoint = ref<number>(30)

function init() {
  const pointFollow = getLocalStorage('pointFollow', {})
  publicTime.value = pointFollow.publicTime || { min: 0, max: 12 }
  noFollowGoods.value = pointFollow.noFollowGoods ?? []
  followGoodsType.value = pointFollow.followGoodsType ?? []
  minPoint.value = pointFollow.minPoint ?? 30
}

export function setPointFollow() {
  // 持久化存储不指定商品名称关注的数据
  setLocalStorage('pointFollow', {
    publicTime: publicTime.value || { min: 0, max: 12 },
    noFollowGoods: noFollowGoods.value ?? [],
    followGoodsType: followGoodsType.value ?? [],
    minPoint: minPoint.value ?? 30,
  })
}

async function getRewards(consignment_id: string, itemId: number | undefined) {
  const localData = getLocalStorage('appearanceBaseInfo', {})
  if (!itemId)
    return 0
  if (localData?.[itemId])
    return Number(localData[itemId]?.rewards) || 0
  const res = await getAppearanceBaseInfoApi(consignment_id)
  if (res) {
    const data = res.data.additional_data
    localData[itemId] = data
    setLocalStorage('appearanceBaseInfo', localData)
    await new Promise(resolve => setTimeout(resolve, 200))
    return Number(data?.rewards) || 0
  }
  else {
    return 0
  }
}

/**
 * 是否关注商品
 * @param goods 商品信息
 */
async function isFollowGoods(goods: GoodsDetail) {
  const rewards = await getRewards(goods.consignment_id, goods.attrs.item_index)
  if (rewards === 0)
    return false
  return (
    !goods.is_followed
    && !noFollowGoods.value.includes(goods.info)
    && (rewards / goods.single_unit_price * 100) >= minPoint.value
    && ((goods.remaining_time <= publicTime.value.max * 60 * 60 && goods.remaining_time >= publicTime.value.min * 60 * 60) || goods.state === 5)
  )
}

export async function autoFollowByPoint() {
  if (isStopFollow.value) {
    errNotify('正在自动关注，请勿重复操作')
    return
  }
  showAutoFollowLoading()
  let page = 1
  let currentFollowGoodsNum = await getCurrentFollowGoodsNum()
  // 循环获取商品列表
  try {
    while (isStopFollow.value) {
      // 如果关注的商品数量超过10，则停止关注
      if (currentFollowGoodsNum >= 20) {
        errNotify(`已经达到最大关注数 20 个，已停止关注`)
        hideAutoFollowLoading()
        break
      }
      const res = await getFollowByPointListApi(followGoodsType.value.join(','), page)
      if (res?.data?.list?.length === 0) {
        hideAutoFollowLoading()
        break
      }
      const goodsList = res.data.list as GoodsDetail[]
      for (const goods of goodsList) {
        if (await isFollowGoods(goods)) {
          const isFollow = await followGoods(goods)
          if (isFollow) {
            currentFollowGoodsNum++
            if (currentFollowGoodsNum >= 20) {
              errNotify(`已经达到最大关注数 20 个，停止关注`)
              hideAutoFollowLoading()
              return
            }
          }
          else {
            hideAutoFollowLoading()
          }
        }
        // 只关注120元以下的
        if (goods.single_unit_price > 120 * 100) {
          hideAutoFollowLoading()
          return
        }
      }

      if (page % 4 === 0)
        await new Promise(resolve => setTimeout(resolve, 2000))

      page++
    }
  }
  catch (e) {
    hideAutoFollowLoading()
  }
}

export function usePointFollow() {
  init()
  return { publicTime, noFollowGoods, followGoodsType, minPoint }
}
