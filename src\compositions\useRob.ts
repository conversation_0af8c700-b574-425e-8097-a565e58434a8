import { computed, ref } from 'vue'
import { Screen } from 'quasar'
import { addFollow<PERSON><PERSON>, cancelFollow<PERSON>pi, getAppearanceBaseInfoApi, getMyFollowList, getSummaryDataApi } from '~/api/wbl'
import type { AppearanceBaseInfo, FollowItem, TableColumns } from '~/type'

import { errNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import { getLocalStorage, isLogin, setLocalStorage } from '~/utils/utils'
import { getGoodsFollowedNumAndItemId, getGoodsInfo, getLocalGoodsInfo } from '~/compositions/useGoodsInfo'
import { deleteClockNotification, getClockIsExist } from '~/compositions/useClock'
import { setCurrRole } from '~/compositions/useCurrRole'

/**
 * 抢购表格加载状态
 */
export const robTableLoading = ref(false)
// 表格列
export const columns: TableColumns<FollowItem>[] = [
  { name: 'name', label: '商品', align: 'left', field: 'goods_name', headerStyle: 'min-width: 200px' },
  {
    name: 'goodsType',
    align: 'left',
    label: '类型',
    field: 'goods_type_name',
    headerStyle: 'min-width: 85px',
    format: (val: string, row: FollowItem) => `${val}${row.role_sect}`,
  },
  { name: 'state', align: 'center', sortable: true, label: '状态', field: 'state', headerStyle: 'min-width: 94px' },
  {
    name: 'goods_price',
    label: '价格',
    field: 'goods_price',
    align: 'left',
    sortable: true,
    sort: (a: number, b: number) => a - b,
  },
  {
    align: 'left',
    sortable: true,
    name: 'followed_num',
    label: '关注',
    field: 'followed_num',
    format: (val: number) => val ?? 0,
  },
  {
    name: 'rewards',
    align: 'left',
    label: '积分',
    field: 'appearanceBaseInfo',
    sortable: true,
    sort: (a: AppearanceBaseInfo, b: AppearanceBaseInfo) => {
      if (a?.rewards && b?.rewards)
        return Number(a.rewards) - Number(b.rewards)
      else if (a?.rewards)
        return 1
      else
        return -1
    },
  },
  {
    align: 'left',
    sortable: true,
    name: 'minPublicPrice',
    label: '公示最低价',
    field: 'minPublicPrice',
    format: (val: number) => val / 100 || 0,
  },
  {
    align: 'left',
    sortable: true,
    name: 'minSalePrice',
    label: '在售最低价',
    field: 'minSalePrice',
    format: (val: number) => val / 100 || 0,
  },
  {
    align: 'left',
    sortable: true,
    name: 'minAvgPrice',
    label: '最低均价',
    field: 'minAvgPrice',
    format: (val: number) => Math.floor(val / 100) || 0,
  },
  { align: 'left', sortable: true, name: 'sellCount', label: '交易热度', field: 'sellCount', format: (val: number) => val || 0 },
  { align: 'left', sortable: true, name: 'followHeat', label: '实穿热度', field: 'followHeat', format: (val: number) => val || 0 },
  {
    name: 'date',
    label: '结束时间',
    align: 'left',
    field: 'remaining_time',
    format: (val: number, row) => val === 0 ? '已失效' : new Date(row?.end_time || (new Date().getTime() + val * 1e3)).toLocaleString(),
    classes: (row: FollowItem) => (row.remaining_time < 600 && row.remaining_time > 0) ? 'text-red' : '',
    sortable: true,
  },
  { name: 'consignment_id', align: 'left', label: 'id', field: 'consignment_id' },
  { name: 'action', label: '操作', field: 'action', align: 'center' },
]

// 是否显示抢购
const isShowRob = ref(false)
// 表格数据
export const rows = ref<FollowItem[]>([])
export const selectedGoodsType = ref<string[]>([])
export const goodsTypes = computed(() => {
  // 获取row中的goodsType和role_sect并去重
  const goodsTypeSet = new Set<string>()
  rows.value.forEach((item) => {
    goodsTypeSet.add(`${item.goods_type_name}${item.role_sect}`)
  })
  return Array.from(goodsTypeSet)
})
// 获取我的关注列表
export async function getList(isLazy = false) {
  if (robTableLoading.value)
    return
  robTableLoading.value = true
  try {
    const followList = await getMyFollowList()
    if (followList) {
      const goods2 = followList.data.follow[2].list
      const goods3 = followList.data.follow[3].list
      if (isLazy && rows.value.length === (goods2.length + goods3.length)) {
        robTableLoading.value = false
        return
      }
      const statePriority: { [key: number]: number } = {
        5: 1, // 在售期
        3: 2, // 公示期
        6: 3, // 已售出
        7: 4, // 已下架
      }
      const goodsTypeSet = new Set<string>()
      rows.value = [...goods2, ...goods3].sort((a: FollowItem, b: FollowItem) => {
        // 3:公示期 5:在售期 6:已售出 7:已下架
        // 在售期间排在最前面，公示期次之，已售出次之，已下架最后
        // 在售期优先于公示期，状态为5的排在最前面，如果状态相同则按照结束时间排序
        // 如果状态不同，根据状态的优先级排序
        if (a.state !== b.state)
          return statePriority[a.state] - statePriority[b.state]

        // 如果状态相同，按照结束时间排序
        return a.remaining_time - b.remaining_time
      }).map((item) => {
        goodsTypeSet.add(`${item.goods_type_name}${item.role_sect}`)
        const end_time = new Date().getTime() + item.remaining_time * 1e3
        if (item.type === 3) {
          const localGoodsInfo = getLocalGoodsInfo(item.goods_name)
          if (localGoodsInfo) {
            return {
              ...item,
              end_time,
              clock: getClockIsExist(item),
              minAvgPrice: localGoodsInfo.minAvgPrice,
              sellCount: localGoodsInfo.sellCount,
              followHeat: localGoodsInfo.followHeat,
            }
          }
        }
        return { ...item, end_time, clock: false }
      }) as FollowItem[]
      selectedGoodsType.value = Array.from(goodsTypeSet)
      // 添加clock属性，方便通知提醒
    }
    else {
      rows.value = []
    }
    getAdditionalInfo().then(() => {
      robTableLoading.value = false
    })
  }
  catch (error) {
    errNotify(`获取关注列表失败${error}`)
    robTableLoading.value = false
  }
}

export function setGoodsStateById(id: string, state: number) {
  const row = rows.value.find(item => item.consignment_id === id)
  if (row) {
    row.state = state
    row.remaining_time = 0
    const statePriority: { [key: number]: number } = {
      5: 1, // 在售期
      3: 2, // 公示期
      6: 3, // 已售出
      7: 4, // 已下架
    }
    rows.value = rows.value.sort((a: FollowItem, b: FollowItem) => {
      if (a.state !== b.state)
        return statePriority[a.state] - statePriority[b.state]
      // 如果状态相同，按照结束时间排序
      return a.remaining_time - b.remaining_time
    }).map(item => item) as FollowItem[]
  }
}

async function getAppearanceBaseInfo(consignment_id: string, itemId: number) {
  const localData = getLocalStorage('appearanceBaseInfo', {})
  if (localData?.[itemId])
    return localData[itemId]
  const res = await getAppearanceBaseInfoApi(consignment_id)
  if (res) {
    const data = res.data.additional_data
    localData[itemId] = data
    setLocalStorage('appearanceBaseInfo', localData)
    await new Promise(resolve => setTimeout(resolve, 200))
    return data
  }
}

async function getRoleSummaryDate(consignment_id: string) {
  // 先检查本次会话是否已经获取过该商品的基本信息
  const localData = JSON.parse(sessionStorage.getItem('roleSummaryDate') || '{}')
  if (localData?.[consignment_id])
    return localData[consignment_id]
  const res = await getSummaryDataApi(consignment_id)
  if (res) {
    const data = res.data.summary_data
    localData[consignment_id] = data
    sessionStorage.setItem('roleSummaryDate', JSON.stringify(localData))
    await new Promise(resolve => setTimeout(resolve, 200))
    return data
  }
}

/**
 * 异步获取商品信息，并更新到表格，如果同一商品名已经获取过一次，则不再请求，外观只请求关注数
 */
async function getAdditionalInfo() {
  if (rows.value.length === 0)
    return
    // 定义一个临时对象，存放goodsInfo，避免重复请求
  const goodsInfoMap: { [key: string]: any } = {}
  for (const item of rows.value) {
    try {
      if (isShowRob.value === false)
        return

      const { followed_num, itemId } = await getGoodsFollowedNumAndItemId(item.consignment_id, item.type)
      item.followed_num = followed_num
      await new Promise(resolve => setTimeout(resolve, 500))
      if (Screen.sm || Screen.xs || Screen.md)
        continue
      if (item.type === 3) {
        item.appearanceBaseInfo = await getAppearanceBaseInfo(item.consignment_id, itemId)
        let goodsInfo = goodsInfoMap[item.goods_name]
        if (goodsInfo === undefined) {
          goodsInfo = await getGoodsInfo(item.goods_name, item.consignment_id)
          goodsInfoMap[item.goods_name] = goodsInfo
          await new Promise(resolve => setTimeout(resolve, 800))
        }
        item.minPublicPrice = goodsInfo.minPublicPrice
        item.minSalePrice = goodsInfo.minSalePrice
        item.minAvgPrice = goodsInfo.minAvgPrice
        item.sellCount = goodsInfo.sellCount
        item.followHeat = goodsInfo.followHeat
      }
      else if (item.type === 2) {
        item.role_summary_data = await getRoleSummaryDate(item.consignment_id)
      }
    }
    catch (error) {
      console.error('Failed to get additional info:', error)
    }
  }
}

// 输入框的关注id
const inputFollowId = ref('')

export function useAction() {
  /**
   * 取消关注
   * @param id 商品ID
   * @param goodsType 商品类型 2:角色 3:外观
   */
  const cancelFollow = async (id: string, goodsType: number) => {
    const res = await cancelFollowApi(id, goodsType)
    if (res?.code === 1) {
      rows.value = rows.value.filter(item => item.consignment_id !== id)
      deleteClockNotification(id)
      successNotify('已成功取消关注')
    }
  }

  const cancelAllInvalidFollowLoading = ref(false)
  /**
   * 批量取消失效商品的关注
   */
  const cancelAllInvalidFollow = async () => {
    cancelAllInvalidFollowLoading.value = true
    const invalidList = rows.value.filter(item => item.state === 6 || item.state === 7)
    if (invalidList.length === 0) {
      warningNotify('没有失效的商品')
      cancelAllInvalidFollowLoading.value = false
      return
    }
    for (const [index, item] of invalidList.entries()) {
      await cancelFollow(item.consignment_id, item.type)
      // 为了防止请求过快，延迟一下2000ms,否则会出现请求过快导致部分商品无法取消关注,如果是最后一个商品则不延迟
      if (index !== invalidList.length - 1)
        await new Promise(resolve => setTimeout(resolve, 2000))
    }
    successNotify('已成功取消所有失效商品的关注')
    cancelAllInvalidFollowLoading.value = false
  }

  /**
   * 添加关注
   * @param goodsType 商品类型 2:角色 3:外观
   */
  const addFollow = async (goodsType: number) => {
    inputFollowId.value = inputFollowId.value.trim()
    if (!inputFollowId.value) {
      warningNotify('商品id不能为空')
      return
    }
    // id必须为19位纯数字
    else if (!/^\d{19}$/.test(inputFollowId.value)) {
      warningNotify('商品id格式错误')
      return
    }
    const res = await addFollowApi(inputFollowId.value, goodsType)
    // 添加成功，刷新表格，清空输入框
    if (res?.code === 1) {
      successNotify('已成功添加关注')
      inputFollowId.value = ''
      // 刷新表格
      await getList()
    }
  }

  return { cancelFollow, addFollow, inputFollowId, cancelAllInvalidFollow, cancelAllInvalidFollowLoading }
}

export function useRob() {
  const openRob = async () => {
    if (await isLogin()) {
      isShowRob.value = true
      await setCurrRole()
      // 加载表格数据
      await getList(true)
    }
  }
  const closeRob = () => {
    isShowRob.value = false
  }
  const states: { [key: number]: string } = { 3: '公示中', 5: '在售中', 6: '已售出', 7: '已下架' }
  const statesColor: { [key: number]: string } = { 3: 'red', 5: 'green', 6: 'grey', 7: 'grey' }

  const stateColor = (el: number) => (statesColor[el] || 'grey')
  const state = (el: number) => (states[el] || '其他')
  return { openRob, isShowRob, closeRob, stateColor, state }
}
