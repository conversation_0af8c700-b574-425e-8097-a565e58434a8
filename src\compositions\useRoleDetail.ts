import { computed, ref } from 'vue'

const isShowGoodsDetailDialog = ref(false)
const currId = ref('')
const currGoodsType = ref(2)

export function openGoodsDetailDialog(id: string, goodsType: number) {
  currId.value = id
  currGoodsType.value = goodsType
  isShowGoodsDetailDialog.value = true
}
export function useGoodsDetail() {
  const currType = computed(() => currGoodsType.value === 2 ? 'role' : 'skin')
  return { isShowGoodsDetailDialog, currId, currType }
}
