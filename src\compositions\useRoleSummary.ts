import { ref } from 'vue'

function calculatePosition(e: MouseEvent): { [key: string]: string } {
  const { left, top, width, height } = (e.target as HTMLElement).getBoundingClientRect()
  const centerX = left + width / 2
  const centerY = top + height / 2
  const winWidth = window.innerWidth
  const winHeight = window.innerHeight
  return {
    top: centerY <= winHeight / 2 ? `${Math.max(centerY + 15, 0)}px` : '',
    bottom: centerY > winHeight / 2 ? `${Math.max(winHeight - centerY + 15, 0)}px` : '',
    left: centerX <= winWidth / 2 ? `${Math.max(centerX + 50, 0)}px` : '',
    right: centerX > winWidth / 2 ? `${Math.max(winWidth - centerX + 50, 0)}px` : '',
  }
}

export function useRoleSummary() {
  const isShowRoleSummary = ref(false)
  const position = ref({ })

  function onMouseEnter(e: MouseEvent) {
    position.value = calculatePosition(e)
    isShowRoleSummary.value = true
  }

  function onMouseLeave() {
    isShowRoleSummary.value = false
  }

  return {
    isShowRoleSummary,
    position,
    onMouseEnter,
    onMouseLeave,
  }
}
