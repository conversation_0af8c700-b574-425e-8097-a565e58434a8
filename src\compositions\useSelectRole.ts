import { onMounted, ref } from 'vue'
import { getGatewaysApi, getRolesApi, selectRole<PERSON>pi } from '~/api/m_wbl'
import { errNotify } from '~/compositions/useNotify'
import { accountTokenList } from '~/compositions/useMultiOpen'
import { setSessionStorage } from '~/utils/utils'
import { setTokenApi } from '~/api/wbl'

export const isShowSelectRoleDialog = ref(false)
const currToken = ref('')
const selectServer = ref()
const selectRole = ref()
const serverList = ref<ServerOption[]>([])
const roleList = ref<RoleOption[]>([])

export function openSelectRoleDialog(token = '') {
  selectServer.value = null
  selectRole.value = null
  if (location.host === 'm.seasunwbl.com') {
    isShowSelectRoleDialog.value = true
    currToken.value = ''
  }
  else if (token) {
    currToken.value = token
    isShowSelectRoleDialog.value = true
  }
}
interface ServerOption {
  name: string
  id: string
}
interface RoleOption {
  freeze: number
  name: string
  level: number
  id: string
}

export function useSelectRole() {
  onMounted(async () => {
    const res = await getGatewaysApi()
    const serverArr: ServerOption[] = []
    res.data.list.forEach((item: any) => {
      item.servers.forEach((server: any) => {
        serverArr.push({ name: `${item.zone_name} - ${server.server_name}`, id: server.server_id })
      })
    })
    serverList.value = serverArr
  })
  async function selectServerHandler() {
    const res = await getRolesApi(selectServer.value.id, currToken.value)
    if (res.code === 1) {
      roleList.value = res.data.list as RoleOption[]
    }
    else {
      roleList.value = []
      selectRole.value = null
      errNotify(res.msg)
    }
    await setTokenApi()
  }

  async function submit() {
    if (!selectServer.value || !selectRole.value) {
      errNotify('请选择服务器和角色')
      return
    }
    const res = await selectRoleApi(selectServer.value.id, selectRole.value.id, currToken.value)
    if (res.code === 1) {
      isShowSelectRoleDialog.value = false
      if (currToken.value) {
        const account = accountTokenList.value.find(item => item.token === currToken.value)
        if (account) {
          account.isSelectedRole = true
          account.roleInfo = `${selectServer.value.name} - ${selectRole.value.name}`
          setSessionStorage('accountTokenList', accountTokenList.value)
        }
      }
    }
    else {
      errNotify(res.msg)
    }
    await setTokenApi()
  }

  return {
    submit,
    roleList,
    selectRole,
    selectServerHandler,
    serverList,
    selectServer,
  }
}
