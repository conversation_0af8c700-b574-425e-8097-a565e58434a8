import { onMounted, onUnmounted, ref } from 'vue'
import { Screen } from 'quasar'
import { getLocalStorage, setLocalStorage } from '~/utils/utils'

export const isShowSettingDialog = ref(false)
export const preRobTime = ref(getLocalStorage('preRobTime', 150))
export const payRetryTimes = ref(getLocalStorage('payRetryTimes', 3))
export const openServiceRepeatDelayTime = ref(getLocalStorage('openServiceRepeatDelayTime', 5000))
// 订单重试次数
export const orderRetryTimes = ref(getLocalStorage('orderRetryTimes', 2))
// 订单重试延迟
export const orderRetryDelayTime = 100
// 支付重试延迟
export const payRetryDelayTime = 200
// 用来判断不是在维护页面的情况
export const isMaintenanceRef = ref(false)
export const currPayWay = ref<'phone' | 'qrcode'>('phone')
export const payWays = ref([
  { label: '跳转支付宝', value: 'phone' },
  { label: '扫描二维码', value: 'qrcode' },
])
export function onPayWayChange() {
  setLocalStorage('payWay', currPayWay.value)
}
export function initPayWay() {
  const localPayWay = getLocalStorage('payWay', null) as 'phone' | 'qrcode' | null
  if (localPayWay) {
    currPayWay.value = localPayWay
  }
  else {
    if (Screen.xs) {
      currPayWay.value = 'phone'
      setLocalStorage('payWay', 'phone')
    }
    else {
      currPayWay.value = 'qrcode'
      setLocalStorage('payWay', 'qrcode')
    }
  }
}

// 订单验证码失效时间 min
export const orderCaptchaTime = 20

// 是否开启自动隐藏菜单，仅PC有效
export const isAutoHideMenu = ref(getLocalStorage('isAutoHideMenu', false))
const currentClass = ref('')
function handleMouseMove(event: MouseEvent) {
  // 获取鼠标的位置
  const x = event.clientX
  const y = event.clientY

  // 获取屏幕的大小
  const width = window.innerWidth
  const height = window.innerHeight

  // 检查鼠标是否在屏幕的右下角 400 px 100 px
  if (x > width - 130 && y > height - 430)
    currentClass.value = 'slide-in-right'
  else
    currentClass.value = 'slide-out-right'
}
export function onAutoHideMenuChange() {
  window.removeEventListener('mousemove', handleMouseMove)
  if (isAutoHideMenu.value) {
    // 检查是否有鼠标移动事件，没有则添加
    window.addEventListener('mousemove', handleMouseMove)
  }
  else {
    currentClass.value = ''
  }
  setLocalStorage('isAutoHideMenu', isAutoHideMenu.value)
}
const isHideMenu = ref(getLocalStorage('phoneIsHideMenu', false))
export function useAutoHideMenu() {
  // 手机端使用手动隐藏、开启菜单
  function toggleMenu() {
    isHideMenu.value = !isHideMenu.value
    setLocalStorage('phoneIsHideMenu', isHideMenu.value)
    if (isHideMenu.value)
      currentClass.value = 'slide-out-right'

    else
      currentClass.value = 'slide-in-right'
  }
  // 判断是否开启自动隐藏菜单，如果开启则监听鼠标移动事件，否则不监听
  if (isAutoHideMenu.value && !Screen.xs) {
    onMounted(() => {
      window.addEventListener('mousemove', handleMouseMove)
    })
    onUnmounted(() => {
      window.removeEventListener('mousemove', handleMouseMove)
    })
  }
  else if (Screen.xs && isHideMenu.value) {
    currentClass.value = 'slide-out-right'
  }
  else {
    currentClass.value = ''
  }
  return { currentClass, toggleMenu, isHideMenu }
}

export const loginCaptchaId = '01355fca4924adfab1a23bc485cde58c'
export const orderCaptchaId = '03f8ecb9b7a6a5f69d237bc888ff56d6'
