import { computed, ref } from 'vue'
import { GM_info } from '$'
import { errNotify, successNotify } from '~/compositions/useNotify'
import { getLocalStorage, setLocalStorage } from '~/utils/utils'

const updateUrl = `https://7363-script-login-7gl4kl3kda68f130-1253020038.tcb.qcloud.la/qianqian/jx3-wbl-tools.user.js`
const metaUrl = `https://7363-script-login-7gl4kl3kda68f130-1253020038.tcb.qcloud.la/qianqian/jx3-wbl-tools.meta.js`

const currentVersion = GM_info.script.version
const newVersion = ref(getLocalStorage('newVersion', ''))
export const newVersionText = computed(() => {
  if (newVersion.value !== '' && newVersion.value > currentVersion)
    return `，最新版本：${newVersion.value}`
  else
    return `，已是最新版本`
})
function checkUpdate() {
  if (!(sessionStorage.getItem('isCheckedUpdate') === 'true')) {
    sessionStorage.setItem('isCheckedUpdate', 'true')
    // 从 GreasyFork 获取最新版本
    fetch(metaUrl)
      .then(response => response.text())
      .then((pageContent) => {
        // 使用正则表达式从页面内容中提取版本信息
        const match = pageContent.match(/@version\s*(.+)/)
        if (match) {
          const latestVersion = match[1]
          newVersion.value = latestVersion
          setLocalStorage('newVersion', latestVersion)
          // 比较版本
          if (latestVersion > currentVersion)
            successNotify('魔法书有新版本可用，请更新！')
        }
      })
  }
}

const updateBtnIsLoading = ref(false)
async function redirectToInstallPage(installLink: string) {
  try {
    const response = await fetch(installLink, { method: 'HEAD' })
    if (response.ok) {
      successNotify('正在跳转到安装页面，请稍后...')
      window.location.href = installLink
      updateBtnIsLoading.value = false
    }
    else {
      updateBtnIsLoading.value = false
      errNotify('获取版本信息失败')
    }
  }
  catch (error) {
    errNotify('获取版本信息失败')
    updateBtnIsLoading.value = false
  }
}
let count = 0
function openUpdateWeb() {
  count++
  if (newVersion.value !== '' && newVersion.value <= currentVersion && count % 10 !== 0) {
    successNotify('已是最新版本')
    return
  }
  updateBtnIsLoading.value = true
  redirectToInstallPage(updateUrl).then(() => {
    updateBtnIsLoading.value = false
  })
}
export function useUpdate() {
  return { openUpdateWeb, checkUpdate, updateBtnIsLoading, currentVersion }
}
