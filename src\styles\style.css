#tampermonkey-container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
}

*, html, body {
    vertical-align: middle !important;
}

.hover-loading {
    position: absolute;
    width: 80px;
    padding: 8px;
    aspect-ratio: 1;
    border-radius: 50%;
    background: #25b09b;
    --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0, #000 0) content-box;
    -webkit-mask: var(--_m);
    mask: var(--_m);
    -webkit-mask-composite: source-out;
    mask-composite: subtract;
    animation: l3 1s infinite linear;
}
@keyframes l3 {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(1turn);
    }
}
.hover-content {
    position: absolute;
    font-size: 24px;
    color: rgb(191, 191, 191);
    user-select: none;
}

.popUpImgFixed {
    --min-img-height: 360px;
    --min-img-width: 640px;
    display: none;
    justify-content: center;
    align-items: center;
    background: url(//jx3wbl.xoyocdn.com/img/bg-empty.13dfbabf.png) center top
    no-repeat rgb(247, 253, 255);
    position: fixed;
    z-index: 99999;
    border: 1px solid #69c3ca;
    border-radius: 8px;
    color: transparent;
    user-select: none;
    min-height: var(--min-img-height);
    min-width: var(--min-img-width);
}
.popUpImg {
    object-fit: contain;
    border-radius: 8px;
    min-height: var(--min-img-height);
    min-width: var(--min-img-width);
    max-height: 100vh;
    max-width: 60vw;
    -webkit-user-drag: none;
}
