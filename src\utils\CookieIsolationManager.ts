/**
 * Cookie隔离管理器（简化版）
 * 解决多开抢号过程中Cookie冲突问题
 * 注意：由于GM_cookie API不可用，移除了复杂的Cookie恢复功能，改为页面刷新方案
 */

// Cookie隔离状态
interface IsolationState {
  isIsolated: boolean
  isolationStartTime: number
}

class CookieIsolationManager {
  private state: IsolationState = {
    isIsolated: false,
    isolationStartTime: 0,
  }

  /**
   * 开始Cookie隔离模式
   * 在多开抢号开始前调用（仅用于多开抢号场景）
   */
  startIsolation(): void {
    if (this.state.isIsolated) {
      console.warn('Cookie隔离已经启动，跳过重复启动')
      return
    }

    this.state.isIsolated = true
    this.state.isolationStartTime = Date.now()
    console.log('Cookie隔离模式已启动（多开抢号）')
  }

  /**
   * 结束Cookie隔离模式
   * 在多开抢号完成后调用（仅用于多开抢号场景）
   */
  endIsolation(): void {
    if (!this.state.isIsolated) {
      console.warn('Cookie隔离未启动，跳过结束操作')
      return
    }

    this.state.isIsolated = false
    this.state.isolationStartTime = 0
    console.log('Cookie隔离模式已结束（多开抢号）')
  }

  /**
   * 检查是否处于隔离模式
   */
  isInIsolationMode(): boolean {
    return this.state.isIsolated
  }

  /**
   * 获取隔离状态信息
   */
  getIsolationState(): IsolationState {
    return { ...this.state }
  }

  /**
   * 强制结束隔离（用于异常情况）
   */
  forceEndIsolation(): void {
    console.warn('强制结束Cookie隔离模式')
    this.endIsolation()
  }

  /**
   * 检查隔离是否超时（超过30分钟自动结束）
   */
  checkIsolationTimeout(): void {
    if (this.state.isIsolated && this.state.isolationStartTime > 0) {
      const elapsed = Date.now() - this.state.isolationStartTime
      const timeout = 30 * 60 * 1000 // 30分钟

      if (elapsed > timeout) {
        console.warn('Cookie隔离超时，自动结束隔离模式')
        this.forceEndIsolation()
      }
    }
  }
}

// 创建全局单例
export const cookieIsolationManager = new CookieIsolationManager()

// 定期检查隔离超时
setInterval(() => {
  cookieIsolationManager.checkIsolationTimeout()
}, 60000) // 每分钟检查一次

// 页面卸载时自动结束隔离
window.addEventListener('beforeunload', () => {
  if (cookieIsolationManager.isInIsolationMode())
    cookieIsolationManager.forceEndIsolation()
})

export default cookieIsolationManager
