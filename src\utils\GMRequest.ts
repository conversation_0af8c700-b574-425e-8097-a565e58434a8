import { cookieIsolationManager } from './CookieIsolationManager'
import { GM_cookie, GM_xmlhttpRequest } from '$'

interface Response {
  data: any
  code: number
  msg: string
}

export class GMRequest {
  getByToken(url: string, token: string, preserveMainCookie = false) {
    return new Promise<Response>((resolve, reject) => {
      // 检查是否处于Cookie隔离模式
      const isInIsolationMode = cookieIsolationManager.isInIsolationMode()

      // 如果处于隔离模式，设置当前隔离的token用于监控
      if (isInIsolationMode)
        cookieIsolationManager.setCurrentIsolatedToken(token)

      GM_xmlhttpRequest({
        method: 'GET',
        url,
        cookie: `m_gray_switch=1; m_gray_switch_=1; ts_session_id=${token}; ts_session_id_=${token}`,
        headers: {
          'Content-Type': 'application/json',
        },
        onload(response) {
          // 只清理灰度相关的cookie，保留主要的登录cookie
          GM_cookie.delete({ name: 'm_gray_token', url: '.seasunwbl.com' })
          GM_cookie.delete({ name: 'm_gray_token_', url: '.seasunwbl.com' })

          // Cookie清理策略：
          // 1. 如果明确要求保留主cookie（账号切换场景），则不清理主要cookie
          // 2. 如果处于Cookie隔离模式（多开抢号场景），则不清理主要cookie以避免冲突
          // 3. 其他情况按原逻辑清理
          const shouldPreserveCookie = preserveMainCookie || isInIsolationMode

          if (!shouldPreserveCookie) {
            GM_cookie.delete({ name: 'm_gray_switch', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'm_gray_switch_', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'ts_session_id', url: '.seasunwbl.com' })
            GM_cookie.delete({ name: 'ts_session_id_', url: '.seasunwbl.com' })
          }
          else if (isInIsolationMode) {
            // 在隔离模式下，记录日志以便调试
            console.log(`Cookie隔离模式：跳过清理主要cookie，当前请求token: ${token.substring(0, 8)}...`)
          }

          resolve(JSON.parse(response.responseText))
        },
        onerror(error) {
          reject(error)
        },
      })
    })
  }

  // 专门用于账号切换的安全请求方法，不会清理主要的登录cookie
  getByTokenSafe(url: string, token: string) {
    return this.getByToken(url, token, true)
  }
}
