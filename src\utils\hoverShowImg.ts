// hoverShowImg.ts
import { getLocalStorage, setLocalStorage } from '~/utils/utils'

interface GoodsImgUrls {
  [key: string]: string
}
const goodsImgUrls: GoodsImgUrls = getLocalStorage('originGoodImgUrls', null)
class HoverShowImg {
  private domSelectors: { [key: string]: string }
  private abortController: AbortController
  private lastPromise: Promise<string> | null // 最后一个promise
  private popUpImgFixedDiv: HTMLDivElement | null = null
  private popUpImg: HTMLImageElement | null = null
  private hoverContentDiv: HTMLDivElement | null = null
  private hoverLoadingDiv: HTMLDivElement | null = null
  constructor() {
    this.domSelectors = {
      aTentList: '.app-web-page-follow-index-m__followComponent--TEzXY',
      aTentState: '.app-web-page-follow-components-styles-components-m__flexWrap--1vtQV',
      roleCloth: 'app-web-components-modal-components-role-detail-styles-index-m__positionRelative--3gVPE',
    }
    this.abortController = new AbortController()
    this.lastPromise = null
  }

  public init(): void {
    this.addTooltipImg()
    const pathName = location.pathname
    if (pathName === '/follow') {
      this.waitDomBySelector(this.domSelectors.aTentList)
        .then(() => {
          this.observerDom(document.querySelector(this.domSelectors.aTentList) as HTMLDivElement, this.aTentListObserverCallback.bind(this))
        })
        .catch((error) => {
          console.error('Error waiting for element:', error)
        })
    }
    if (pathName === '/follow' || pathName === '/buyer' || pathName === '/role')
      this.observerDom(document.body, this.hoverImgCallback.bind(this))
    if (pathName === '/buyer')
      this.observerSkin()
  }

  /**
   * 是否是剩余时间dom变化
   */
  private isTimeChange(mutation: MutationRecord) {
    return (mutation.type === 'childList'
      && mutation.addedNodes.length === 1
      && mutation.removedNodes.length === 0
      && mutation.addedNodes[0].nodeName === '#text')
  }

  private async aTentListObserverCallback(mutationsList: MutationRecord[]) {
    for (const mutation of mutationsList) {
      if (this.isTimeChange(mutation)) {
        const stateDom = (mutation.target as Element).closest(this.domSelectors.aTentState)!.nextElementSibling as HTMLDivElement
        if (location.pathname === '/follow' && location.search === '?t=skin') {
          const infoDiv = stateDom.parentElement!.children[0] as HTMLDivElement
          const name = infoDiv.textContent || ''
          this.setShowImgEventListener(infoDiv, name, -40, 100)
        }
      }
    }
  }

  private waitDomBySelector(selector: string, timeout = 5000): Promise<HTMLElement> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      const waitDom = () => {
        const dom = document.querySelector(selector) as HTMLElement
        if (dom !== null)
          resolve(dom)
        else if (Date.now() - startTime > timeout)
          reject(new Error(`Timeout waiting for ${selector}`))
        else
          requestAnimationFrame(waitDom)
      }
      waitDom()
    })
  }

  // 幽馥引·绛（替）·衣
  private replaceReg = /(·（替）)$|(（替）)/
  private replaceMap = new Map([['清光彩璨礼盒', '清光彩璨·琉华礼盒']])

  private preprocessName(name: string): string {
    if (this.replaceReg.test(name))
      return name.replace(this.replaceReg, '')
    return this.replaceMap.get(name) || name
  }

  public isSetHoverImg(src: string, name: string, type: string = ''): boolean {
    if (goodsImgUrls?.[name])
      return true
    // 完整匹配
    const blockUrls = new Set([
      'https://jx3wbl.xoyocdn.com/img/icon-no-equipment-resource.d1bae5d2.jpg',
      'https://dl.pvp.xoyo.com/prod/icons/baoxiang12.png?v=2',
      'https://dl.pvp.xoyo.com/prod/icons/biaoju09.png?v=2',
    ])
    // 模糊匹配
    const blockUrlKeyWords = [
      'https://dl.pvp.xoyo.com/prod/icons/item_21_7_5_',
      'https://dl.pvp.xoyo.com/prod/icons/item_23_9_26',
      'https://dl.pvp.xoyo.com/prod/icons/tome',
    ]
    // 正则匹配以 xxx 结尾
    const blockNameReg = /·(一|二|三|四|五)$/
    // 正则匹配以 xxx 开头
    const blockNameRegPre = /^(发型)·/
    // 模糊匹配关键词
    const blockKeyWords = ['芽芽', '表情包', '佩囊']
    // 完整匹配关键词
    const blockNames = new Set(['耳绒绒'])

    const typeBlock = new Set(['佩囊', '宠物', '马具', '手饰', '背挂', '腰挂'])
    const isTypeBlocked = type !== '' && typeBlock.has(type)
    const isUrlBlocked = blockUrls.has(src) || blockUrlKeyWords.some(item => src.includes(item))
    const isNameBlocked = blockNameReg.test(name) || blockNameRegPre.test(name) || blockNames.has(name) || blockKeyWords.some(item => name.includes(item))

    return !isTypeBlocked && !isUrlBlocked && !isNameBlocked
  }

  /**
   * 添加悬浮框图片
   * @private
   */
  private addTooltipImg(): void {
    // 创建 popUpImgFixed div
    const popUpImgFixedDiv = document.createElement('div')
    popUpImgFixedDiv.classList.add('popUpImgFixed')

    // 创建 div
    const div = document.createElement('div')
    popUpImgFixedDiv.appendChild(div)

    // 创建 img
    const img = document.createElement('img')
    img.classList.add('popUpImg')
    div.appendChild(img)

    // 创建 hover-content div
    const hoverContentDiv = document.createElement('div')
    hoverContentDiv.classList.add('hover-content')
    hoverContentDiv.textContent = '暂无外观展示图'
    popUpImgFixedDiv.appendChild(hoverContentDiv)

    // 创建 hover-loading div
    const hoverLoadingDiv = document.createElement('div')
    hoverLoadingDiv.classList.add('hover-loading')
    popUpImgFixedDiv.appendChild(hoverLoadingDiv)

    popUpImgFixedDiv.addEventListener('mouseenter', () => {
      // 鼠标进入悬浮框时保持显示状态
      popUpImgFixedDiv.style.display = 'flex'
    })

    popUpImgFixedDiv.addEventListener('mouseleave', () => {
      // 鼠标离开悬浮框时隐藏悬浮框
      popUpImgFixedDiv.style.display = 'none'
    })
    this.popUpImgFixedDiv = popUpImgFixedDiv
    this.popUpImg = img
    this.hoverContentDiv = hoverContentDiv
    this.hoverLoadingDiv = hoverLoadingDiv
    // 将 popUpImgFixedDiv 添加到文档中
    document.body.appendChild(popUpImgFixedDiv)
  }

  private showImgLoading(): void {
    this.popUpImg!.src = ''
    this.popUpImg!.style.display = 'none'
    this.hoverContentDiv!.style.display = 'none'
    this.hoverLoadingDiv!.style.display = 'block'
  }

  private hideImgLoading(): void {
    this.hoverLoadingDiv!.style.display = 'none'
  }

  private showImgError(): void {
    this.popUpImg!.style.display = 'none'
    this.popUpImg!.src = ''
    this.hoverContentDiv!.style.display = 'block'
    this.hideImgLoading()
  }

  private showImgSuccess(src: string): void {
    this.popUpImg!.src = src
    this.popUpImg!.onload = () => {
      this.popUpImg!.style.display = 'block'
      this.hoverContentDiv!.style.display = 'none'
      this.adjustElementPosition()
      this.hideImgLoading()
    }
  }

  /**
   * Adjust the position of the element if it overflows the screen.
   * @private
   */
  private adjustElementPosition() {
  // Get the dimensions of the element
    const { height, width } = this.popUpImgFixedDiv!.getBoundingClientRect()

    // Get the position style of the element
    const elementStyle = this.popUpImgFixedDiv!.style
    const elementLeft = elementStyle.left
    const elementRight = elementStyle.right
    const elementTop = elementStyle.top
    const elementBottom = elementStyle.bottom

    // Calculate the new position of the element
    if (elementLeft && elementTop) {
    // Top-left layout, check if the bottom and right sides overflow
      if (Number.parseInt(elementTop) + height > window.innerHeight)
        elementStyle.top = `${window.innerHeight - height}px`
      if (Number.parseInt(elementLeft) + width > window.innerWidth)
        elementStyle.left = `${window.innerWidth - width}px`
    }
    else if (elementRight && elementTop) {
    // Top-right layout, check if the bottom and left sides overflow
      if (Number.parseInt(elementTop) + height > window.innerHeight)
        elementStyle.top = `${window.innerHeight - height}px`
      if (Number.parseInt(elementRight) + width > window.innerWidth)
        elementStyle.right = `${window.innerWidth - width}px`
    }
    else if (elementLeft && elementBottom) {
    // Bottom-left layout, check if the top and right sides overflow
      if (Number.parseInt(elementBottom) + height > window.innerHeight)
        elementStyle.bottom = `${window.innerHeight - height}px`
      if (Number.parseInt(elementLeft) + width > window.innerWidth)
        elementStyle.left = `${window.innerWidth - width}px`
    }
    else if (elementRight && elementBottom) {
    // Bottom-right layout, check if the top and left sides overflow
      if (Number.parseInt(elementBottom) + height > window.innerHeight)
        elementStyle.bottom = `${window.innerHeight - height}px`
      if (Number.parseInt(elementRight) + width > window.innerWidth)
        elementStyle.right = `${window.innerWidth - width}px`
    }
  }

  private showHoverImg(position: { [p: string]: string }): void {
    this.showImgLoading()
    Object.assign(this.popUpImgFixedDiv!.style, position)
    this.popUpImgFixedDiv!.style.display = 'flex'
  }

  private hideHoverImg(): void {
    this.popUpImgFixedDiv!.style.display = 'none'
  }

  private checkImage(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = function () {
        resolve(true)
      }
      img.onerror = function () {
        resolve(false)
      }
      img.src = url
    })
  }

  private checkImages(urls: string[]): Promise<string> {
    const promises = urls.map(async (url) => {
      const isValid = await this.checkImage(url)
      if (isValid)
        return url // 返回有效的URL
      else
        return Promise.reject(new Error('URL无效')) // 如果URL无效，将Promise拒绝
    })
    return Promise.any(promises)
  }

  private getLocalImgUrl(name: string): { url: string, updateTime: number } | null {
    return getLocalStorage('imgUrls', {})?.[name] || null
  }

  private updateLocalImgUrl(name: string, url: string): void {
    const imgUrls = getLocalStorage('imgUrls', {})
    imgUrls[name] = { url, updateTime: new Date().getTime() }
    setLocalStorage('imgUrls', imgUrls)
  }

  private async getImageFromUrls(name: string, urls: string[]): Promise<string> {
    try {
      const url = await this.checkImages(urls)
      this.updateLocalImgUrl(name, url)
      return url
    }
    catch (e) {
      this.updateLocalImgUrl(name, '暂无展示外观图')
      return ''
    }
  }

  private getClothUrl(name: string) {
    return `https://dl.pvp.xoyo.com/prod/icons/handbook/image/${name}-%E8%AF%A6%E6%83%85-1.png`
  }

  private getShopUrl(name: string) {
    return `https://seasun-jx3-esport-static.ks3-cn-shanghai.ksyun.com/prod/icons/webmall/image/${name}-%E8%AF%A6%E6%83%85.png`
  }

  private generateUrlTypes(name: string): string[] {
    const types = ['礼盒', '·礼盒']
    const plusReg = /·(白发|金发)$/
    types.unshift(plusReg.test(name) ? '·豪华' : '·标准')
    return types
  }

  private checkGoodsImgUrls(names: string[]): string[] {
    return names.filter(name => goodsImgUrls?.[name]).map(name => goodsImgUrls[name])
  }

  private generateUrls(baseName: string, types: string[]): string[] {
    const newBaseName = baseName.replaceAll('·', '')
    const goodsNames = [baseName, ...types.map(item => baseName + item)]
    if (baseName !== newBaseName)
      goodsNames.push(newBaseName, ...types.map(item => newBaseName + item))
    const localUrls = this.checkGoodsImgUrls(goodsNames)
    if (localUrls.length)
      return localUrls
    const baseUrls = goodsNames.map(item => this.getClothUrl(item))
    const shopUrls = goodsNames.map(item => this.getShopUrl(item))
    return [...baseUrls, ...shopUrls]
  }

  private isBox(name: string): boolean {
    const reg = /·(黑发|衣|外装|左|右|白发|金发|披风|帽|发)$/
    return !reg.test(name)
  }

  private async getImgUrlByName(name: string): Promise<string> {
    if (!name)
      return ''
    if (goodsImgUrls?.[name])
      return goodsImgUrls[name]
    name = this.preprocessName(name)
    const localUrl = this.getLocalImgUrl(name)
    if (localUrl) {
      const isDisplayImage = localUrl.url !== '暂无展示外观图'
      const currentTime = new Date().getTime()
      if (!isDisplayImage && localUrl.updateTime + 24 * 60 * 60 * 1e3 > currentTime) {
        return ''
      }
      else if (isDisplayImage && localUrl.updateTime + 60 * 24 * 60 * 60 * 1e3 > currentTime) {
        return localUrl.url
      }
      else if (isDisplayImage && localUrl.updateTime + 60 * 24 * 60 * 60 * 1e3 < currentTime) {
        // 检查链接是否正常
        const isUrlValid = await this.checkImage(localUrl.url)
        if (isUrlValid) {
          this.updateLocalImgUrl(name, localUrl.url)
          return localUrl.url
        }
      }
    }

    const baseUrl = this.getClothUrl(name)
    const isBaseUrlValid = await this.checkImage(baseUrl)
    if (isBaseUrlValid) {
      this.updateLocalImgUrl(name, baseUrl)
      return baseUrl
    }
    else {
      const shopUrl = this.getShopUrl(name)
      const isShopUrlValid = await this.checkImage(shopUrl)
      if (isShopUrlValid) {
        this.updateLocalImgUrl(name, shopUrl)
        return shopUrl
      }
    }
    const types = this.generateUrlTypes(name)
    if (!this.isBox(name)) {
      const reg = /·(黑发|衣|外装|左|右|白发|金发|披风|帽|发)$/
      const baseName = name.replace(reg, '')
      const urls = this.generateUrls(baseName, types)
      return await this.getImageFromUrls(name, urls)
    }
    else {
      const roleClothesReg = /^(黑发|白发|金发|发|帽)·/
      if (roleClothesReg.test(name)) {
        const baseName = name.replace(roleClothesReg, '')
        const urls = this.generateUrls(baseName, types)
        return await this.getImageFromUrls(name, urls)
      }
      else {
        const urls = this.generateUrls(name, types)
        return await this.getImageFromUrls(name, urls)
      }
    }
  }

  public async hoverImgMouseEnter(e: MouseEvent, goodsName: string, yOffset: number = 60, xOffset: number = 38) {
    this.cancelLastAsyncOperation()
    const position = this.calculatePosition(e, yOffset, xOffset)
    this.showHoverImg(position)

    this.lastPromise = this.getImgUrlByName(goodsName)
    try {
      const src = await this.lastPromise
      if (src)
        this.showImgSuccess(src)
      else
        this.showImgError()
    }
    catch (error) {
      this.handleError(error)
      this.showImgError()
    }
  }

  public hoverImgMouseLeave() {
    this.cancelLastAsyncOperation()
    this.hideHoverImg()
  }

  private setShowImgEventListener(triggerDom: HTMLElement, goodsName: string, yOffset: number = 60, xOffset: number = 38): void {
    if (!goodsName)
      return

    triggerDom.style.cursor = 'pointer'

    const handleMouseEnter = async (e: MouseEvent) => {
      await this.hoverImgMouseEnter(e, goodsName, yOffset, xOffset)
    }

    const handleMouseLeave = () => {
      this.hoverImgMouseLeave()
    }

    triggerDom.addEventListener('mouseenter', handleMouseEnter)
    triggerDom.addEventListener('mouseleave', handleMouseLeave)
  }

  private cancelLastAsyncOperation(): void {
    if (this.lastPromise) {
      this.abortController.abort()
      this.abortController = new AbortController()
      this.lastPromise = null
    }
  }

  private calculatePosition(e: MouseEvent, yOffset: number, xOffset: number): { [key: string]: string } {
    const { left, top, width, height } = (e.target as HTMLElement).getBoundingClientRect()
    const centerX = left + width / 2
    const centerY = top + height / 2
    const winWidth = window.innerWidth
    const winHeight = window.innerHeight

    return {
      top: centerY <= winHeight / 2 ? `${Math.max(centerY + yOffset, 0)}px` : '',
      bottom: centerY > winHeight / 2 ? `${Math.max(winHeight - centerY + yOffset, 0)}px` : '',
      left: centerX <= winWidth / 2 ? `${Math.max(centerX + xOffset, 0)}px` : '',
      right: centerX > winWidth / 2 ? `${Math.max(winWidth - centerX + xOffset, 0)}px` : '',
    }
  }

  private handleError(error: any): void {
    if (error.name === 'AbortError')
      console.error('操作已取消')
    else
      console.error(error)
  }

  private hoverImgCallback(mutations: MutationRecord[]): void {
    mutations.forEach((mutation) => {
      // 检查每个 mutation 中新增的节点
      mutation.addedNodes.forEach(async (node: any) => {
        // 判断新增节点是否为目标 div
        if (node instanceof HTMLElement && node.className === this.domSelectors.roleCloth) {
          await this.waitDomBySelector('.lazyload-wrapper')
          const nodeList = node.querySelectorAll('.lazyload-wrapper')
          for (const item of Array.from(nodeList)) {
            const contentType = item.querySelector('div.lazyload-wrapper>div>div>div:nth-child(2)')?.textContent || ''
            const whiteList = new Set(['发型：', '成衣：', '披风：', '挂宠：', '眼饰：', '佩囊：', '面部挂件：'])
            if (whiteList.has(contentType)) {
              const contentList = item.querySelector('div.lazyload-wrapper>div>div:nth-child(2)') as HTMLDivElement
              const content = contentList.textContent
              if (content !== '无') {
                const children = contentList.children as HTMLCollection
                for (const child of Array.from(children)) {
                  const name = child.textContent || ''
                  const imgDom = child.querySelector('img') as HTMLImageElement
                  if (this.isSetHoverImg(imgDom.src, name)) {
                    // 设置点击跳转搜索
                    imgDom.style.cursor = 'pointer'
                    imgDom.addEventListener('click', () => {
                      window.open(`https://jx3.seasunwbl.com/buyer?appearance_name=${name}&t=skin`)
                    })
                    // 设置悬浮图片
                    this.setShowImgEventListener(imgDom, name, 16, 30)
                  }
                }
              }
            }
          }
        }
      })
    })
  }

  private setHoverByQuery(dom?: HTMLElement) {
    // 选择所有匹配的div元素
    let elements
    if (dom)
      elements = dom.querySelectorAll('.app-web-components-role-item-components-AvatarWithInfomation-index-m__container--3LZEN')
    else
      elements = document.querySelectorAll('.app-web-components-role-item-components-AvatarWithInfomation-index-m__container--3LZEN')
      // 遍历所有选中的元素
    elements.forEach((element) => {
      // 获取每个元素的第一个子div
      const firstChildDiv = element.firstElementChild
      // 如果存在子div，则删除它
      if (firstChildDiv && firstChildDiv.tagName === 'DIV')
        firstChildDiv.remove()
      const parentElement = element.parentNode as HTMLDivElement
      // 获取父亲节点的下一个兄弟节点
      const nextSibling = parentElement?.nextElementSibling
      if (nextSibling && nextSibling.textContent)
        this.setShowImgEventListener(element as HTMLDivElement, nextSibling.textContent, -150, 50)
    })
  }

  private observerSkin() {
    if (location.search === '?t=skin')
      this.setHoverByQuery()
    // 创建一个观察器实例并传入回调函数
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有节点被添加
          mutation.addedNodes.forEach((addedNode) => {
            const skinItemClass = 'app-web-page-buyer-components-skin-list-index-m__skinItem--2ek4U'
            const skinTableTitleClass = 'app-web-page-buyer-components-buyer-table-header-index-m__container--2Sb7L'
            if (addedNode instanceof Element) {
              // 监听到变化就直接加
              if (addedNode.className === skinItemClass) {
                const ele = addedNode.querySelector('.app-web-components-role-item-components-AvatarWithInfomation-index-m__container--3LZEN') as HTMLDivElement
                const firstChildDiv = ele.firstElementChild
                if (firstChildDiv && firstChildDiv.tagName === 'DIV')
                  firstChildDiv.remove()
                const parentElement = ele.parentNode as HTMLDivElement
                // 获取父亲节点的下一个兄弟节点
                const nextSibling = parentElement.nextElementSibling
                if (nextSibling && nextSibling.textContent)
                  this.setShowImgEventListener(ele, nextSibling.textContent, -150, 50)
              }
              // 监听到父级组件
              else if (addedNode.firstElementChild?.className === skinTableTitleClass) {
                this.setHoverByQuery(addedNode as HTMLDivElement)
              }
            }
          })
        }
      })
    })
    // 配置观察器选项:
    const config = {
      childList: true,
      subtree: true,
    }
    observer.observe(document.body, config)
  }

  private observerDom(dom: Node, callback: (mutations: MutationRecord[]) => void): void {
    // 观察者配置项
    const options = {
      childList: true, // 观察目标节点的子节点的新增和删除
      subtree: true, // 观察目标节点的所有后代节点的新增和删除
    }
    // 创建一个观察者对象
    const observer = new MutationObserver(callback)
    observer.observe(dom, options)
  }
}

export const hoverShowImg = new HoverShowImg()
