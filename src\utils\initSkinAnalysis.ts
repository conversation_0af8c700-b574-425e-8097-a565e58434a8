import { createApp } from 'vue'
import { Notify, Quasar } from 'quasar'
import SkinAnalysis from '~/SkinAnalysis.vue'

function addTab() {
  const tabDiv = document.querySelector('div.app-web-components-tabs-index-m__tabs--2gRHL.margin') as HTMLDivElement
  const tabItem = document.createElement('div')
  if (window.location.search === '?t=skinAnalysis')
    tabItem.className = 'app-web-components-tabs-index-m__tabItem--39MUd app-web-components-tabs-index-m__active--1BXBp'
  else
    tabItem.className = 'app-web-components-tabs-index-m__tabItem--39MUd'
  tabItem.style.width = '120px'
  tabItem.innerHTML = `<div class="app-web-components-tabs-index-m__tabItemText--2KYEV app-web-components-tabs-index-m__fs16--34kOR">外观分析</div><span class="app-web-components-tabs-index-m__downTriangle--2M0p7"></span>`
  tabItem.onclick = () => {
    if (window.location.search !== '?t=skinAnalysis')
      window.location.search = '?t=skinAnalysis'
  }
  // tabDiv 所有子元素加上onclick事件，如果点击了，外观分析就改为未被选中状态
  for (const child of Array.from(tabDiv.children) as HTMLDivElement[]) {
    child.onclick = () => {
      const tabContent = document.getElementById('skinAnalysis-container')
      if (tabContent)
        tabContent.remove()
      tabItem.className = 'app-web-components-tabs-index-m__tabItem--39MUd'
    }
  }
  tabDiv?.append(tabItem)
}

function addContent() {
  if (window.location.search === '?t=skinAnalysis') {
    const div = document.querySelector('div.app-web-page-buyer-index-m__buyComponent--2QiFV') as HTMLDivElement
    createApp(SkinAnalysis).use(Quasar, {
      plugins: { Notify }, // import Quasar plugins and add here
      config: {
        notify: { position: 'top' },
      },
    }).mount(
      (() => {
        const app = document.createElement('div')
        app.id = 'skinAnalysis-container'

        div.append(app)
        return app
      })(),
    )
  }
}

export function initAnalysis() {
  if (window.location.host === 'jx3.seasunwbl.com' && window.location.pathname === '/buyer') {
    addTab()
    addContent()
  }
}
