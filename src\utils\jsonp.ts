import { errNotify } from '~/compositions/useNotify'
import { unsafeWindow } from '$'

interface UnsafeWindow extends Window {
  [key: string]: any
}

function generateUrlWithCallback(url: string) {
  const callbackId = Date.now()
  const callbackName = `geetest_${callbackId}`
  url += `${!url.includes('?') ? '?' : '&'}callback=${callbackName}`
  return { url, callbackName }
}

export function jsonp(url: string, callback: (data: any) => void) {
  const { url: urlWithCallback, callbackName } = generateUrlWithCallback(url)
  const script = document.createElement('script')
  script.src = urlWithCallback
  script.async = true
  document.head.appendChild(script)

  // 清理函数
  script.onload = () => {
    script.remove()
    delete (unsafeWindow as UnsafeWindow)[callbackName]
  }

  // 错误处理
  script.onerror = () => {
    script.remove()
    delete (unsafeWindow as UnsafeWindow)[callbackName]
    errNotify('请求失败')
  }

  // 定义全局的回调函数
  (unsafeWindow as UnsafeWindow)[callbackName] = (data: any) => {
    callback(data)
  }
}
