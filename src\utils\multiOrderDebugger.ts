/**
 * 多账户订单创建调试工具
 * 用于诊断和验证多账户模式下的订单创建和二维码显示问题
 */

import { ref, watchEffect } from 'vue'
import { accountTokenList, currQrcode, zfbUrls } from '~/compositions/useMultiOpen'
import { cookieIsolationManager } from '~/utils/CookieIsolationManager'

interface DebugLog {
  timestamp: string
  level: 'info' | 'warn' | 'error'
  category: string
  message: string
  data?: any
}

const debugLogs = ref<DebugLog[]>([])

function addDebugLog(level: 'info' | 'warn' | 'error', category: string, message: string, data?: any) {
  const log: DebugLog = {
    timestamp: new Date().toISOString(),
    level,
    category,
    message,
    data,
  }
  debugLogs.value.push(log)
  console.log(`[MultiOrderDebugger] ${level.toUpperCase()} [${category}] ${message}`, data || '')
}

/**
 * 检查多账户模式状态
 */
export function checkMultiOrderState() {
  addDebugLog('info', 'STATE_CHECK', '开始检查多账户模式状态')

  // 检查账号列表
  const accountCount = accountTokenList.value.length
  addDebugLog('info', 'ACCOUNTS', `多开账号数量: ${accountCount}`, accountTokenList.value.map(acc => ({
    account: acc.account,
    hasToken: !!acc.token,
    isSelectedRole: acc.isSelectedRole,
  })))

  // 检查二维码状态
  addDebugLog('info', 'QRCODE', `主账号二维码状态: ${currQrcode.value ? '已生成' : '未生成'}`)
  addDebugLog('info', 'QRCODE', `多开账号二维码状态: ${zfbUrls.value.filter(url => url).length}/${zfbUrls.value.length} 已生成`)

  // 检查Cookie隔离状态
  const isolationState = cookieIsolationManager.getIsolationState()
  addDebugLog('info', 'COOKIE_ISOLATION', 'Cookie隔离状态', isolationState)

  return {
    accountCount,
    mainQrcodeGenerated: !!currQrcode.value,
    multiQrcodeGenerated: zfbUrls.value.filter(url => url).length,
    isolationActive: isolationState.isIsolated,
    logs: debugLogs.value,
  }
}

/**
 * 监控订单创建流程
 */
export function startOrderMonitoring() {
  addDebugLog('info', 'MONITORING', '开始监控订单创建流程')

  // 监控二维码状态变化
  const stopWatchingQrcode = watchEffect(() => {
    if (currQrcode.value) {
      addDebugLog('info', 'QRCODE_UPDATE', '主账号二维码已更新', {
        length: currQrcode.value.length,
        preview: `${currQrcode.value.substring(0, 50)}...`,
      })
    }
  })

  const stopWatchingMultiQrcode = watchEffect(() => {
    zfbUrls.value.forEach((url, index) => {
      if (url) {
        addDebugLog('info', 'QRCODE_UPDATE', `账号${index + 1}二维码已更新`, {
          length: url.length,
          preview: `${url.substring(0, 50)}...`,
        })
      }
    })
  })

  return {
    stop: () => {
      stopWatchingQrcode()
      stopWatchingMultiQrcode()
      addDebugLog('info', 'MONITORING', '停止监控订单创建流程')
    },
  }
}

/**
 * 验证修复效果
 */
export function validateFixes() {
  addDebugLog('info', 'VALIDATION', '开始验证修复效果')

  const issues: string[] = []
  const state = checkMultiOrderState()

  // 检查1: 账号列表是否正常
  if (state.accountCount === 0)
    issues.push('没有配置多开账号')

  // 检查2: 二维码状态初始化
  if (currQrcode.value && !state.mainQrcodeGenerated)
    issues.push('主账号二维码状态异常')

  // 检查3: Cookie隔离状态
  if (state.isolationActive)
    addDebugLog('warn', 'VALIDATION', 'Cookie隔离模式仍处于激活状态，可能需要手动结束')

  // 检查4: 错误处理机制
  const hasErrorHandling = typeof window !== 'undefined' && 'addEventListener' in window
  if (!hasErrorHandling)
    issues.push('错误处理机制不可用')

  if (issues.length === 0)
    addDebugLog('info', 'VALIDATION', '所有检查通过，修复效果良好')
  else
    addDebugLog('error', 'VALIDATION', `发现 ${issues.length} 个问题`, issues)

  return {
    success: issues.length === 0,
    issues,
    state,
  }
}

/**
 * 清理调试日志
 */
export function clearDebugLogs() {
  debugLogs.value = []
  addDebugLog('info', 'SYSTEM', '调试日志已清理')
}

/**
 * 导出调试日志
 */
export function exportDebugLogs() {
  const logData = {
    timestamp: new Date().toISOString(),
    logs: debugLogs.value,
    state: checkMultiOrderState(),
  }

  const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `multi-order-debug-${Date.now()}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  addDebugLog('info', 'EXPORT', '调试日志已导出')
}

// 导出调试工具
export const multiOrderDebugger = {
  checkState: checkMultiOrderState,
  startMonitoring: startOrderMonitoring,
  validate: validateFixes,
  clearLogs: clearDebugLogs,
  exportLogs: exportDebugLogs,
  getLogs: () => debugLogs.value,
}
