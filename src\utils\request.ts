import { getSessionStorage, handlingErrors } from '~/utils/utils'
import { GM_xmlhttpRequest } from '$'

const defaultOptions = {
  method: 'GET',
  credentials: 'include', // 携带跨域Cookie
  headers: {
    'Content-Type': 'application/json',
  },
}

async function handleResponse(response: Response, isThrowErr: boolean) {
  if (!response.ok) {
    handlingErrors(new Error(`${response.status} ${response.statusText}`))
    return
  }

  const data = await response.json()
  if (isThrowErr) {
    return data
  }
  else if (data?.code !== 1) {
    handlingErrors(new Error(`${data.msg}`))
    return
  }

  return data
}

export const httpWithErr = async function customFetch(url: string, options?: any, isThrowErr = true) {
  // 合并传入的选项和默认选项
  options = { ...defaultOptions, ...options }
  try {
    const response = await fetch(url, options)
    return handleResponse(response, isThrowErr)
  }
  catch (error) {
    handlingErrors(error)
  }
}

export const http = async function customFetch(url: string, options?: any) {
  return httpWithErr(url, options, false)
}

// 专门用于需要session认证的请求，确保携带必要的cookie
export const httpWithSession = async function sessionFetch(url: string, options?: any, isThrowErr = true): Promise<any> {
  // 从sessionStorage获取token并构建cookie字符串
  const token = JSON.parse(sessionStorage.getItem('currToken') || '') as string
  // const token = sessionStorage.getItem('currToken')
  return new Promise((resolve, reject) => {
    const cookieString = token
      ? `ts_session_id=${token}; ts_session_id_=${token}; m_gray_switch=1; m_gray_switch_=1`
      : 'm_gray_switch=1; m_gray_switch_=1'

    // 合并默认headers和传入的headers
    const headers = {
      'Content-Type': 'application/json',
      ...options?.headers,
    }

    GM_xmlhttpRequest({
      method: options?.method || 'GET',
      url,
      cookie: cookieString,
      data: options?.body,
      headers,
      onload(response) {
        try {
          const data = JSON.parse(response.responseText)
          if (isThrowErr) {
            resolve(data)
          }
          else if (data?.code !== 1) {
            handlingErrors(new Error(`${data.msg}`))
            resolve(undefined)
          }
          else {
            resolve(data)
          }
        }
        catch (error) {
          handlingErrors(new Error(`解析响应失败: ${error}`))
          reject(error)
        }
      },
      onerror(error) {
        handlingErrors(error)
        reject(error)
      },
    })
  })
}
