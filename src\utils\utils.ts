import { JSEncrypt } from 'jsencrypt'
import { errNotify, warningNotify } from '~/compositions/useNotify'
import { GM_getValue, GM_setValue } from '$'
import { getBaseInfoApi } from '~/api/wbl'

/**
 * 获取ts参数
 */
export function getTs() {
  return (new Date()).getTime()
}

let reqId = ''
/**
 * 获取请求需要的 reqId ,随机生成
 * @param length 长度 默认 32
 * @returns {string} reqId
 */
export function getReqId(length = 32): string {
  if (reqId === '') {
    const characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const charactersLength = characters.length
    let randomString = ''
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charactersLength)
      randomString += characters[randomIndex]
    }
    reqId = randomString
    return randomString
  }
  else {
    return reqId
  }
}

export function handlingErrors(e: any, msg?: string) {
  if (e instanceof Error)
    errNotify(msg || `${e.message}`)
  else
    errNotify('未知错误')
}

/**
 * 是否选择了角色
 * @param goods_type 商品类型 2:角色 3:外观
 */
export async function isHaveRole(goods_type: number = 3) {
  if (goods_type === 2)
    return true
  const role = document.querySelector('.app-web-components-head-components-UserInfo-components-RoleInfoHandler-index-m__secondaryColor--1-yHP') as HTMLDivElement
  if (role && role?.textContent === '请选择角色') {
    warningNotify('请先选择角色')
    role.click()
    return false
  }
  else {
    const res = await getBaseInfoApi()
    if (res.code === -4) {
      warningNotify(res.msg)
      return false
    }
    return true
  }
}

/**
 * 是否登录
 */
export async function isLogin(isRemoteVerify = false) {
  const login = document.querySelector('.app-web-components-head-components-UserInfo-index-m__displayFlex--1BN9a .app-web-components-auth-handler-index-m__authWrapper--28yCW') as HTMLSpanElement
  if (login && login?.textContent === '登录') {
    warningNotify('请先登录')
    login.click()
    return false
  }
  if (isRemoteVerify) {
    const res = await getBaseInfoApi()
    if (res.code === -3) {
      warningNotify(res.msg)
      return false
    }
    return true
  }
  return true
}

export function setLocalStorage(key: string, value: any): void {
  try {
    GM_setValue(key, value)
  }
  catch (e) {
    console.error('localStorage set error: ', e)
  }
}

export function getLocalStorage(key: string, defaultValue: any = null): any {
  try {
    return GM_getValue(key, defaultValue)
  }
  catch (e) {
    console.error('localStorage get error: ', e)
    return defaultValue
  }
}

export function getSessionStorage(key: string, defaultValue: any = null): any {
  try {
    const storedValue = sessionStorage.getItem(key)
    return storedValue ? JSON.parse(storedValue) : defaultValue
  }
  catch (e) {
    console.error('sessionStorage get error: ', e)
    return defaultValue
  }
}

export function setSessionStorage(key: string, value: any): void {
  try {
    sessionStorage.setItem(key, JSON.stringify(value))
  }
  catch (e) {
    console.error('sessionStorage set error: ', e)
  }
}

export function openNewWin(url: string) {
  const newWindow = window.open(url)
  if (!newWindow)
    errNotify('打开失败，请检查浏览器是否阻止了弹出窗口')
}

export function encryptPwd(password: string) {
  const encrypt = new JSEncrypt()
  const publicKey = '-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDQbEOhlXZCAttTzvZ9104nAXXJ\n9wklw2gFOv1y1FkKObXymIEON1SkR1hIV21oaP3xXeAubiDbrFnXli15mevkpsyv\nLp6yiXsy04GbnqVozugbmr6BpIGQa/Fy+t0crT3KV4clQ9pnwQjexcFV3WMiaVEu\nVjoJCZI6SaKbAhktywIDAQAB\n-----END PUBLIC KEY-----\n'
  encrypt.setKey(publicKey)
  return encodeURIComponent(encrypt.encrypt(password))
}

export function isRetryOrder(code: number) {
  // -10004 验证码错误
  return code && code === -10004
}
