import path from 'node:path'
import fs from 'node:fs'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import monkey from 'vite-plugin-monkey'
import Unocss from 'unocss/vite'
import tsconfigPaths from 'vite-tsconfig-paths'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'

function GenerationMetaJs(fileName: string = 'jx3-wbl-tools') {
  return {
    name: 'user-script-meta',
    async writeBundle() {
      const filePath = path.resolve(__dirname, `./dist/${fileName}.user.js`)
      const fileContent = fs.readFileSync(filePath, 'utf-8')

      const metaStart = fileContent.indexOf('// ==UserScript==')
      const metaEnd = fileContent.indexOf('// ==/UserScript==') + '// ==/UserScript=='.length

      if (metaStart !== -1 && metaEnd !== -1) {
        const metaContent = fileContent.slice(metaStart, metaEnd)
        try {
          fs.writeFileSync(path.resolve(__dirname, `./dist/${fileName}.meta.js`), metaContent, { encoding: 'utf8' })
        }
        catch (err) {
          console.error('Failed to generate UserScript meta:', err)
        }
      }
    },
  }
}
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    GenerationMetaJs(),
    vue({
      template: { transformAssetUrls },
    }),
    // @quasar/plugin-vite options list:
    // https://github.com/quasarframework/quasar/blob/dev/vite-plugin/index.d.ts
    quasar(),
    monkey({
      entry: 'src/main.ts',
      userscript: {
        name: '剑三万宝楼魔法书',
        icon: 'https://jx3.seasunwbl.com/favicon.ico',
        namespace: 'jx3',
        match: ['https://*.seasunwbl.com/*', 'https://www.aijx3.cn/w/*', 'https://pc.jx3sh.com/*', 'https://www.jx3sh.com/h5/pages/goods/wbl*'],
        connect: ['seasunwbl.com', 'aijx3.cn', 'jx3sh.com', 'xoyo.com', 'greasyfork.org', 'tencentcloudapi.com'],
        updateURL: 'https://7363-script-login-7gl4kl3kda68f130-1253020038.tcb.qcloud.la/qianqian/jx3-wbl-tools.meta.js',
        downloadURL: 'https://7363-script-login-7gl4kl3kda68f130-1253020038.tcb.qcloud.la/qianqian/jx3-wbl-tools.user.js',
      },
    }),
    Unocss(),
    tsconfigPaths(),
  ],
})
